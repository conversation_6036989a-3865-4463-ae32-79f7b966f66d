#\!/bin/bash
# Start automation in background

echo "🚀 Starting Hemp Database Automation..."

# Ensure logs directory exists
mkdir -p logs

# Activate virtual environment and start automation
source venv_dedup/bin/activate

# Export DATABASE_URL from .env
export DATABASE_URL=$(grep DATABASE_URL .env  < /dev/null |  cut -d '=' -f2- | tr -d '"')

# Start the mega agent coordinator in continuous mode
nohup python src/agents/mega_agent_coordinator_v2.py --continuous 1 > logs/automation_$(date +%Y%m%d_%H%M%S).log 2>&1 &

# Get the PID
PID=$\!
echo $PID > automation.pid

echo "✅ Automation started with PID: $PID"
echo ""
echo "📋 Monitor with:"
echo "   tail -f logs/automation_*.log"
echo ""
echo "🛑 Stop with:"
echo "   kill $(cat automation.pid)"
echo ""
echo "📊 Check database growth:"
echo "   python comprehensive_data_quality_analysis.py | head -20"
