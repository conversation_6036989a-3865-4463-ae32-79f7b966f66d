{"permissions": {"allow": ["mcp__supabase-mcp-server__get_project", "mcp__supabase-mcp-server__list_tables", "mcp__supabase-mcp-server__execute_sql", "mcp__Context7__get-library-docs", "mcp__github__search_issues", "Bash(gh workflow list:*)", "mcp__desktop-commander__list_directory", "mcp__desktop-commander__search_files", "mcp__desktop-commander__read_file", "mcp__desktop-commander__read_multiple_files", "mcp__supabase-mcp-server__list_projects", "Bash(find:*)", "Bash(rm:*)", "Bash(grep:*)", "Bash(git add:*)", "Bash(npm run dev:*)", "<PERSON><PERSON>(touch:*)", "mcp__github__list_pull_requests", "mcp__github__get_pull_request_files", "Bash(git fetch:*)", "Bash(ls:*)", "Bash(git commit:*)", "<PERSON><PERSON>(curl:*)", "mcp__puppeteer__puppeteer_navigate", "mcp__puppeteer__puppeteer_screenshot", "mcp__supabase-mcp-server__list_migrations", "<PERSON><PERSON>(mkdir:*)", "mcp__supabase-mcp-server__apply_migration", "mcp__puppeteer__puppeteer_evaluate", "mcp__puppeteer__puppeteer_click", "<PERSON><PERSON>(nslookup:*)", "<PERSON><PERSON>(host:*)", "Bash(ping:*)", "Bash(bash:*)", "<PERSON><PERSON>(python:*)", "<PERSON><PERSON>(sudo apt-get:*)", "Bash(sudo apt-get install:*)", "Bash(node:*)", "<PERSON><PERSON>(mv:*)", "WebFetch(domain:cloud.google.com)", "mcp__Context7__resolve-library-id", "Bash(npm install:*)", "Bash(npm run check:*)", "Bash(npx tsc:*)", "Bash(npm run build:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(true)", "mcp__desktop-commander__create_directory", "mcp__desktop-commander__move_file", "mcp__desktop-commander__write_file", "Bash(pip3 install:*)", "mcp__brave-search__brave_web_search", "Bash(pip install:*)", "<PERSON><PERSON>(chmod:*)", "Bash(./run_add_products.sh:*)", "<PERSON><PERSON>(source:*)", "Bash(npm run:*)", "mcp__supabase-mcp-server__list_edge_functions", "mcp__supabase-mcp-server__get_logs", "Bash(cp:*)", "mcp__github__get_file_contents", "mcp__github__search_code", "mcp__puppeteer__puppeteer_fill", "mcp__puppeteer__puppeteer_hover", "Bash(export:*)", "<PERSON><PERSON>(poetry init:*)", "Bash(git push:*)", "Bash(pip3 list:*)", "mcp__supabase-mcp-server__deploy_edge_function", "Bash(npm audit:*)", "<PERSON><PERSON>(poetry show:*)", "Bash(npm update:*)", "Bash(npm ls:*)", "<PERSON><PERSON>(gh run list:*)", "mcp__github__list_issues", "mcp__github__get_pull_request", "mcp__github__add_issue_comment", "Bash(git checkout:*)", "Bash(git stash:*)", "Bash(git merge:*)", "mcp__github__search_repositories", "Bash(git pull:*)", "Bash(./hemp db:*)", "Bash(./hemp --help)", "Bash(npx supabase:*)", "mcp__supabase-mcp-server__get_project_url", "mcp__github__merge_pull_request", "<PERSON><PERSON>(dos2unix:*)", "<PERSON><PERSON>(sed:*)", "Bash(.venv/Scripts/python:*)", "Bash(for f in hemp-automation.yml image-generation.yml monitoring.yml status-check.yml weekly-summary.yml)", "Bash(do [ -f \"$f\" ])", "<PERSON><PERSON>(echo:*)", "Bash(done)", "<PERSON><PERSON>(cat:*)", "Bash(./hemp agent research \"hemp building materials\" --features basic --ai-provider deepseek --max-results 2)", "WebFetch(domain:docs.anthropic.com)", "Bash(pip --version)", "Ba<PERSON>(pip3:*)", "Bash(git ls-tree:*)", "Bash(ss:*)", "Bash(git restore:*)", "<PERSON><PERSON>(test:*)", "Bash(NODE_TLS_REJECT_UNAUTHORIZED=0 npm run db:push)", "<PERSON><PERSON>(timeout 10 npm run dev)", "Bash(npm test:*)", "<PERSON><PERSON>(sudo apt:*)", "Bash(sudo apt install:*)", "Bash(psql:*)", "Bash(kill:*)", "Bash(git reset:*)", "Bash(git rm:*)", "Bash(ps:*)", "<PERSON><PERSON>(jq:*)", "Bash(gh pr list:*)", "<PERSON><PERSON>(timeout:*)", "Bash(# Kill existing automation\npkill -f \"\"start_automation.py\"\" || true\n\n# Make the new script executable\nchmod +x start_automation_fixed.py\n\n# Start the fixed automation in background\nsource venv_dedup/bin/activate\nnohup python start_automation_fixed.py --continuous > logs/automation_fixed_$(date +%Y%m%d_%H%M%S).log 2>&1 &\n\necho \"\"Started fixed automation with PID: $!\"\"\necho \"\"Monitor with: tail -f logs/automation_fixed_*.log\"\")", "<PERSON><PERSON>(true sleep:*)", "Bash(git prune:*)", "Bash(gh:*)", "<PERSON><PERSON>(yamllint:*)", "WebFetch(domain:github.com)", "Bash(npx shadcn-ui@latest add:*)", "Bash(npx shadcn@latest add:*)", "Bash(npx:*)", "Bash(ln:*)", "<PERSON><PERSON>(crontab:*)", "<PERSON><PERSON>(screen:*)", "mcp__ide__getDiagnostics", "Bash(pgrep:*)", "<PERSON><PERSON>(printenv)", "Bash(VITE_SUPABASE_URL=https://ktoqznqmlnxrtvubewyz.supabase.co SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt0b3F6bnFtbG54cnR2dWJld3l6Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODQ5MTc3NiwiZXhwIjoyMDY0MDY3Nzc2fQ.hxEiOy6xebv7I4CYJEU49oCEXRvmU6iz2l8Kg-NnvTs python enrich_companies_auto.py)", "Bash(VITE_SUPABASE_URL=https://ktoqznqmlnxrtvubewyz.supabase.co SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt0b3F6bnFtbG54cnR2dWJld3l6Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODQ5MTc3NiwiZXhwIjoyMDY0MDY3Nzc2fQ.hxEiOy6xebv7I4CYJEU49oCEXRvmU6iz2l8Kg-NnvTs python match_products_to_companies.py)", "Bash(VITE_SUPABASE_URL=https://ktoqznqmlnxrtvubewyz.supabase.co SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt0b3F6bnFtbG54cnR2dWJld3l6Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODQ5MTc3NiwiZXhwIjoyMDY0MDY3Nzc2fQ.hxEiOy6xebv7I4CYJEU49oCEXRvmU6iz2l8Kg-NnvTs python add_company_websites.py)", "Bash(VITE_SUPABASE_URL=https://ktoqznqmlnxrtvubewyz.supabase.co SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt0b3F6bnFtbG54cnR2dWJld3l6Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODQ5MTc3NiwiZXhwIjoyMDY0MDY3Nzc2fQ.hxEiOy6xebv7I4CYJEU49oCEXRvmU6iz2l8Kg-NnvTs python classify_company_types.py)", "Bash(venv_dedup/bin/python3:*)", "Bash(./start-frontend.sh:*)", "Bash(./run_agents_cron.sh:*)"], "deny": []}}