{"analysis_date": "2025-07-10T01:09:28.566097", "summary": {"total_products": 926, "duplicates": 226, "low_quality": 0, "cannabis_naming": 21, "image_mismatches": 0}, "detailed_issues": {"duplicates": [{"type": "near_duplicate", "products": [{"id": 56, "name": "95% Hemp Protein Isolate", "agent": "None_quality_improvement"}, {"id": 57, "name": "Hemp Protein Concentrate", "agent": "None_quality_improvement"}], "name_similarity": 0.71, "desc_similarity": 1.0}, {"type": "near_duplicate", "products": [{"id": 2204, "name": "Acoustic Hemp Performance Product", "agent": "Simplified Patent Mining Agent"}, {"id": 2203, "name": "Acoustic Hemp Professional Product", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.78, "desc_similarity": 0.95}, {"type": "near_duplicate", "products": [{"id": 2204, "name": "Acoustic Hemp Performance Product", "agent": "Simplified Patent Mining Agent"}, {"id": 2199, "name": "Acoustic Hemp Studio Product", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.75, "desc_similarity": 0.94}, {"type": "near_duplicate", "products": [{"id": 2203, "name": "Acoustic Hemp Professional Product", "agent": "Simplified Patent Mining Agent"}, {"id": 2199, "name": "Acoustic Hemp Studio Product", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.81, "desc_similarity": 0.94}, {"type": "near_duplicate", "products": [{"id": 2085, "name": "Advanced Hemp Carbon Nanostructure", "agent": "Simplified Patent Mining Agent"}, {"id": 2066, "name": "Advanced Hemp Graphene Nanostructure", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.86, "desc_similarity": 0.96}, {"type": "near_duplicate", "products": [{"id": 2085, "name": "Advanced Hemp Carbon Nanostructure", "agent": "Simplified Patent Mining Agent"}, {"id": 2068, "name": "Advanced Hemp Quantum Dot Nanostructure", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.82, "desc_similarity": 0.94}, {"type": "near_duplicate", "products": [{"id": 2066, "name": "Advanced Hemp Graphene Nanostructure", "agent": "Simplified Patent Mining Agent"}, {"id": 2068, "name": "Advanced Hemp Quantum Dot Nanostructure", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.8, "desc_similarity": 0.94}, {"type": "near_duplicate", "products": [{"id": 2170, "name": "Athletic Hemp Professional Gear", "agent": "Simplified Patent Mining Agent"}, {"id": 2172, "name": "Athletic Hemp Recreational Gear", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.84, "desc_similarity": 0.88}, {"type": "near_duplicate", "products": [{"id": 2172, "name": "Athletic Hemp Recreational Gear", "agent": "Simplified Patent Mining Agent"}, {"id": 2174, "name": "Athletic Hemp Training Gear", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.79, "desc_similarity": 0.84}, {"type": "near_duplicate", "products": [{"id": 2089, "name": "Biodegradable Hemp Biodegradable Plastic", "agent": "Simplified Patent Mining Agent"}, {"id": 2055, "name": "Biodegradable Hemp Compostable Plastic", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.82, "desc_similarity": 0.95}, {"type": "near_duplicate", "products": [{"id": 2055, "name": "Biodegradable Hemp Compostable Plastic", "agent": "Simplified Patent Mining Agent"}, {"id": 2116, "name": "Biodegradable Hemp Flexible Plastic", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.82, "desc_similarity": 0.81}, {"type": "near_duplicate", "products": [{"id": 147, "name": "<PERSON>", "agent": "None_quality_improvement"}, {"id": 311, "name": "Sour Space Candy Hemp Flower", "agent": "None_quality_improvement"}], "name_similarity": 0.76, "desc_similarity": 1.0}, {"type": "near_duplicate", "products": [{"id": 2086, "name": "Cannabinoid Capsule Formulation", "agent": "Simplified Patent Mining Agent"}, {"id": 2074, "name": "Cannabinoid Delivery System Formulation", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.74, "desc_similarity": 0.93}, {"type": "near_duplicate", "products": [{"id": 2086, "name": "Cannabinoid Capsule Formulation", "agent": "Simplified Patent Mining Agent"}, {"id": 2100, "name": "Cannabinoid Formulation Formulation", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.79, "desc_similarity": 0.94}, {"type": "near_duplicate", "products": [{"id": 2074, "name": "Cannabinoid Delivery System Formulation", "agent": "Simplified Patent Mining Agent"}, {"id": 2100, "name": "Cannabinoid Formulation Formulation", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.7, "desc_similarity": 0.93}, {"type": "near_duplicate", "products": [{"id": 2167, "name": "Eco-Friendly Hemp Cleanup Solution", "agent": "Simplified Patent Mining Agent"}, {"id": 2166, "name": "Eco-Friendly Hemp Protection Solution", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.82, "desc_similarity": 0.81}, {"type": "near_duplicate", "products": [{"id": 2167, "name": "Eco-Friendly Hemp Cleanup Solution", "agent": "Simplified Patent Mining Agent"}, {"id": 2165, "name": "Eco-Friendly Hemp Remediation Solution", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.83, "desc_similarity": 0.81}, {"type": "near_duplicate", "products": [{"id": 2166, "name": "Eco-Friendly Hemp Protection Solution", "agent": "Simplified Patent Mining Agent"}, {"id": 2165, "name": "Eco-Friendly Hemp Remediation Solution", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.88, "desc_similarity": 0.96}, {"type": "near_duplicate", "products": [{"id": 1358, "name": "Fresh Hemp Plant Juice", "agent": "leaves_agent_quality_improvement"}, {"id": 1367, "name": "Hemp Plant Poultice", "agent": "leaves_agent_quality_improvement"}], "name_similarity": 0.73, "desc_similarity": 1.0}, {"type": "near_duplicate", "products": [{"id": 265, "name": "Garden of Life Organic Raw Protein - Hemp", "agent": "None_quality_improvement"}, {"id": 74, "name": "Organic Raw Protein - Hemp", "agent": "None_quality_improvement"}], "name_similarity": 0.78, "desc_similarity": 1.0}, {"type": "near_duplicate", "products": [{"id": 2184, "name": "Hemp 3D Printed Manufacturing Material", "agent": "Simplified Patent Mining Agent"}, {"id": 2185, "name": "Hemp Precision Manufacturing Material", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.85, "desc_similarity": 0.96}, {"type": "near_duplicate", "products": [{"id": 2113, "name": "Hemp Acoustic Building Material", "agent": "Simplified Patent Mining Agent"}, {"id": 2111, "name": "Hemp Insulation Building Material", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.81, "desc_similarity": 0.95}, {"type": "near_duplicate", "products": [{"id": 1921, "name": "Hemp Acoustic Panels", "agent": "turbo_generator_quality_improvement"}, {"id": 1658, "name": "<PERSON><PERSON>rd Acoustic Panels", "agent": "None_quality_improvement"}], "name_similarity": 0.89, "desc_similarity": 1.0}, {"type": "near_duplicate", "products": [{"id": 2110, "name": "Hemp Air Filtration System", "agent": "Simplified Patent Mining Agent"}, {"id": 2109, "name": "Hemp Industrial Filtration System", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.81, "desc_similarity": 0.95}, {"type": "near_duplicate", "products": [{"id": 24, "name": "Hemp Animal Bedding", "agent": null}, {"id": 1691, "name": "Hemp Shiv Animal Bedding", "agent": "None_quality_improvement"}], "name_similarity": 0.88, "desc_similarity": 0.13}, {"type": "near_duplicate", "products": [{"id": 2251, "name": "Hemp-Based Air Filter", "agent": "Simplified Patent Mining Agent"}, {"id": 2107, "name": "Hemp-Based Industrial Filter", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.78, "desc_similarity": 0.95}, {"type": "near_duplicate", "products": [{"id": 2251, "name": "Hemp-Based Air Filter", "agent": "Simplified Patent Mining Agent"}, {"id": 2108, "name": "Hemp-Based Water Filter", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.91, "desc_similarity": 0.98}, {"type": "near_duplicate", "products": [{"id": 2221, "name": "Hemp-Based And Material", "agent": "Academic Research Agent"}, {"id": 2245, "name": "Hemp-Based And  Wood Material", "agent": "Academic Research Agent"}], "name_similarity": 0.88, "desc_similarity": 0.43}, {"type": "near_duplicate", "products": [{"id": 2221, "name": "Hemp-Based And Material", "agent": "Academic Research Agent"}, {"id": 2214, "name": "Hemp-Based Of Material", "agent": "Academic Research Agent"}], "name_similarity": 0.89, "desc_similarity": 0.39}, {"type": "near_duplicate", "products": [{"id": 2245, "name": "Hemp-Based And  Wood Material", "agent": "Academic Research Agent"}, {"id": 2246, "name": "Hemp-Based Composite Material", "agent": "Academic Research Agent"}], "name_similarity": 0.76, "desc_similarity": 0.95}, {"type": "near_duplicate", "products": [{"id": 2245, "name": "Hemp-Based And  Wood Material", "agent": "Academic Research Agent"}, {"id": 2244, "name": "Hemp-Based Polymer  Sandwich Material", "agent": "Academic Research Agent"}], "name_similarity": 0.73, "desc_similarity": 0.92}, {"type": "near_duplicate", "products": [{"id": 2045, "name": "Hemp-Based Battery Energy Storage Device", "agent": "Simplified Patent Mining Agent"}, {"id": 2046, "name": "Hemp-Based Electrode Energy Storage Device", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.85, "desc_similarity": 0.95}, {"type": "near_duplicate", "products": [{"id": 2045, "name": "Hemp-Based Battery Energy Storage Device", "agent": "Simplified Patent Mining Agent"}, {"id": 2088, "name": "Hemp-Based Supercapacitor Energy Storage Device", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.8, "desc_similarity": 0.93}, {"type": "near_duplicate", "products": [{"id": 2087, "name": "Hemp-Based Capsule Pharmaceutical", "agent": "Simplified Patent Mining Agent"}, {"id": 2072, "name": "Hemp-Based Delivery System Pharmaceutical", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.76, "desc_similarity": 0.93}, {"type": "near_duplicate", "products": [{"id": 2087, "name": "Hemp-Based Capsule Pharmaceutical", "agent": "Simplified Patent Mining Agent"}, {"id": 2075, "name": "Hemp-Based Formulation Pharmaceutical", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.8, "desc_similarity": 0.94}, {"type": "near_duplicate", "products": [{"id": 2161, "name": "Hemp-Based Cleanup Cleanup", "agent": "Simplified Patent Mining Agent"}, {"id": 2160, "name": "Hemp-Based Protection Cleanup", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.76, "desc_similarity": 0.95}, {"type": "near_duplicate", "products": [{"id": 2161, "name": "Hemp-Based Cleanup Cleanup", "agent": "Simplified Patent Mining Agent"}, {"id": 2162, "name": "Hemp-Based Remediation Cleanup", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.79, "desc_similarity": 0.94}, {"type": "near_duplicate", "products": [{"id": 2206, "name": "Hemp-Based Comfort Animal", "agent": "Simplified Patent Mining Agent"}, {"id": 2208, "name": "Hemp-Based Health Animal", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.78, "desc_similarity": 0.96}, {"type": "near_duplicate", "products": [{"id": 2206, "name": "Hemp-Based Comfort Animal", "agent": "Simplified Patent Mining Agent"}, {"id": 2210, "name": "Hemp-Based Training Animal", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.75, "desc_similarity": 0.96}, {"type": "near_duplicate", "products": [{"id": 2178, "name": "Hemp-Based Commercial Nautical", "agent": "Simplified Patent Mining Agent"}, {"id": 2181, "name": "Hemp-Based Recreational Nautical", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.77, "desc_similarity": 0.85}, {"type": "near_duplicate", "products": [{"id": 2178, "name": "Hemp-Based Commercial Nautical", "agent": "Simplified Patent Mining Agent"}, {"id": 2183, "name": "Hemp-Based Safety Nautical", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.75, "desc_similarity": 0.86}, {"type": "near_duplicate", "products": [{"id": 2155, "name": "Hemp-Based Component Circuit", "agent": "Simplified Patent Mining Agent"}, {"id": 2152, "name": "Hemp-Based Module Circuit", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.79, "desc_similarity": 0.95}, {"type": "near_duplicate", "products": [{"id": 2155, "name": "Hemp-Based Component Circuit", "agent": "Simplified Patent Mining Agent"}, {"id": 2151, "name": "Hemp-Based System Circuit", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.75, "desc_similarity": 0.95}, {"type": "near_duplicate", "products": [{"id": 2246, "name": "Hemp-Based Composite Material", "agent": "Academic Research Agent"}, {"id": 2212, "name": "Hemp-Based Natural  Composites Material", "agent": "Academic Research Agent"}], "name_similarity": 0.85, "desc_similarity": 0.43}, {"type": "near_duplicate", "products": [{"id": 2246, "name": "Hemp-Based Composite Material", "agent": "Academic Research Agent"}, {"id": 2244, "name": "Hemp-Based Polymer  Sandwich Material", "agent": "Academic Research Agent"}], "name_similarity": 0.73, "desc_similarity": 0.92}, {"type": "near_duplicate", "products": [{"id": 2246, "name": "Hemp-Based Composite Material", "agent": "Academic Research Agent"}, {"id": 278, "name": "Hemp Composite Material", "agent": "None_quality_improvement"}], "name_similarity": 0.88, "desc_similarity": 0.13}, {"type": "near_duplicate", "products": [{"id": 2234, "name": "Hemp-Based Containing  Nanofibers Material", "agent": "Academic Research Agent"}, {"id": 2235, "name": "Hemp-Based Containing  Nanofibrils Material", "agent": "Academic Research Agent"}], "name_similarity": 0.96, "desc_similarity": 0.98}, {"type": "near_duplicate", "products": [{"id": 2072, "name": "Hemp-Based Delivery System Pharmaceutical", "agent": "Simplified Patent Mining Agent"}, {"id": 2075, "name": "Hemp-Based Formulation Pharmaceutical", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.72, "desc_similarity": 0.93}, {"type": "near_duplicate", "products": [{"id": 2220, "name": "Hemp-Based Dietary Material", "agent": "Academic Research Agent"}, {"id": 2241, "name": "Hemp-Based Fiber Material", "agent": "Academic Research Agent"}], "name_similarity": 0.88, "desc_similarity": 0.4}, {"type": "near_duplicate", "products": [{"id": 2046, "name": "Hemp-Based Electrode Energy Storage Device", "agent": "Simplified Patent Mining Agent"}, {"id": 2088, "name": "Hemp-Based Supercapacitor Energy Storage Device", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.83, "desc_similarity": 0.93}, {"type": "near_duplicate", "products": [{"id": 2105, "name": "Hemp-Based Exterior Auto Material", "agent": "Simplified Patent Mining Agent"}, {"id": 2102, "name": "Hemp-Based Interior Auto Material", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.94, "desc_similarity": 0.99}, {"type": "near_duplicate", "products": [{"id": 2225, "name": "Hemp-Based Fabricate  Coll Material", "agent": "Academic Research Agent"}, {"id": 2224, "name": "Hemp-Based Of  Nanofibrous Material", "agent": "Academic Research Agent"}], "name_similarity": 0.71, "desc_similarity": 0.93}, {"type": "near_duplicate", "products": [{"id": 2241, "name": "Hemp-Based Fiber Material", "agent": "Academic Research Agent"}, {"id": 2237, "name": "Hemp-Based Flexible Material", "agent": "Academic Research Agent"}], "name_similarity": 0.91, "desc_similarity": 0.42}, {"type": "near_duplicate", "products": [{"id": 2241, "name": "Hemp-Based Fiber Material", "agent": "Academic Research Agent"}, {"id": 2239, "name": "Hemp-Based Nanofibers Material", "agent": "Academic Research Agent"}], "name_similarity": 0.91, "desc_similarity": 0.41}, {"type": "near_duplicate", "products": [{"id": 2241, "name": "Hemp-Based Fiber Material", "agent": "Academic Research Agent"}, {"id": 2214, "name": "Hemp-Based Of Material", "agent": "Academic Research Agent"}], "name_similarity": 0.89, "desc_similarity": 0.47}, {"type": "near_duplicate", "products": [{"id": 2188, "name": "Hemp-Based Flexible Adhesive", "agent": "Simplified Patent Mining Agent"}, {"id": 2187, "name": "Hemp-Based Structural Adhesive", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.72, "desc_similarity": 0.95}, {"type": "near_duplicate", "products": [{"id": 2188, "name": "Hemp-Based Flexible Adhesive", "agent": "Simplified Patent Mining Agent"}, {"id": 2192, "name": "Hemp-Based Water-Based Adhesive", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.78, "desc_similarity": 0.94}, {"type": "near_duplicate", "products": [{"id": 2237, "name": "Hemp-Based Flexible Material", "agent": "Academic Research Agent"}, {"id": 2238, "name": "Hemp-Based Of  With Material", "agent": "Academic Research Agent"}], "name_similarity": 0.79, "desc_similarity": 0.96}, {"type": "near_duplicate", "products": [{"id": 2120, "name": "Hemp-Based Flour Ingredient", "agent": "Simplified Patent Mining Agent"}, {"id": 2097, "name": "Hemp-Based Oil Ingredient", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.88, "desc_similarity": 0.64}, {"type": "near_duplicate", "products": [{"id": 2223, "name": "Hemp-Based For  With Material", "agent": "Academic Research Agent"}, {"id": 2222, "name": "Hemp-Based Microcrystalline Material", "agent": "Academic Research Agent"}], "name_similarity": 0.71, "desc_similarity": 0.92}, {"type": "near_duplicate", "products": [{"id": 2223, "name": "Hemp-Based For  With Material", "agent": "Academic Research Agent"}, {"id": 2238, "name": "Hemp-Based Of  With Material", "agent": "Academic Research Agent"}], "name_similarity": 0.95, "desc_similarity": 0.41}, {"type": "near_duplicate", "products": [{"id": 199, "name": "Hemp-Based Graphene Supercapacitors", "agent": "None_quality_improvement"}, {"id": 189, "name": "Hemp-Based Supercapacitors", "agent": "None_quality_improvement"}], "name_similarity": 0.85, "desc_similarity": 0.17}, {"type": "near_duplicate", "products": [{"id": 199, "name": "Hemp-Based Graphene Supercapacitors", "agent": "None_quality_improvement"}, {"id": 1394, "name": "Hemp Graphene Supercapacitor", "agent": "innovation_agent_quality_improvement"}], "name_similarity": 0.89, "desc_similarity": 0.15}, {"type": "near_duplicate", "products": [{"id": 2143, "name": "Hemp-Based Growth Growing Medium", "agent": "Simplified Patent Mining Agent"}, {"id": 2148, "name": "Hemp-Based Protection Growing Medium", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.85, "desc_similarity": 0.57}, {"type": "near_duplicate", "products": [{"id": 2130, "name": "Hemp-Based Haircare Cosmetic", "agent": "Simplified Patent Mining Agent"}, {"id": 2128, "name": "Hemp-Based Skincare Cosmetic", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.89, "desc_similarity": 0.98}, {"type": "near_duplicate", "products": [{"id": 2208, "name": "Hemp-Based Health Animal", "agent": "Simplified Patent Mining Agent"}, {"id": 2210, "name": "Hemp-Based Training Animal", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.76, "desc_similarity": 0.96}, {"type": "near_duplicate", "products": [{"id": 2107, "name": "Hemp-Based Industrial Filter", "agent": "Simplified Patent Mining Agent"}, {"id": 2108, "name": "Hemp-Based Water Filter", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.78, "desc_similarity": 0.95}, {"type": "near_duplicate", "products": [{"id": 2215, "name": "Hemp-Based Materials Material", "agent": "Academic Research Agent"}, {"id": 2216, "name": "Hemp-Based Natural Material", "agent": "Academic Research Agent"}], "name_similarity": 0.89, "desc_similarity": 0.38}, {"type": "near_duplicate", "products": [{"id": 2152, "name": "Hemp-Based Module Circuit", "agent": "Simplified Patent Mining Agent"}, {"id": 2151, "name": "Hemp-Based System Circuit", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.8, "desc_similarity": 0.97}, {"type": "near_duplicate", "products": [{"id": 2239, "name": "Hemp-Based Nanofibers Material", "agent": "Academic Research Agent"}, {"id": 2224, "name": "Hemp-Based Of  Nanofibrous Material", "agent": "Academic Research Agent"}], "name_similarity": 0.89, "desc_similarity": 0.46}, {"type": "near_duplicate", "products": [{"id": 2212, "name": "Hemp-Based Natural  Composites Material", "agent": "Academic Research Agent"}, {"id": 2213, "name": "Hemp-Based Natural  Reinforced Material", "agent": "Academic Research Agent"}], "name_similarity": 0.79, "desc_similarity": 0.95}, {"type": "near_duplicate", "products": [{"id": 2214, "name": "Hemp-Based Of Material", "agent": "Academic Research Agent"}, {"id": 2238, "name": "Hemp-Based Of  With Material", "agent": "Academic Research Agent"}], "name_similarity": 0.88, "desc_similarity": 0.39}, {"type": "near_duplicate", "products": [{"id": 2097, "name": "Hemp-Based Oil Ingredient", "agent": "Simplified Patent Mining Agent"}, {"id": 2094, "name": "Hemp-Based Protein Ingredient", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.89, "desc_similarity": 0.96}, {"type": "near_duplicate", "products": [{"id": 2146, "name": "Hemp-Based Organic Growing Medium", "agent": "Simplified Patent Mining Agent"}, {"id": 2148, "name": "Hemp-Based Protection Growing Medium", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.81, "desc_similarity": 0.95}, {"type": "near_duplicate", "products": [{"id": 2202, "name": "Hemp-Based Performance Sound", "agent": "Simplified Patent Mining Agent"}, {"id": 2198, "name": "Hemp-Based Professional Sound", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.74, "desc_similarity": 0.95}, {"type": "near_duplicate", "products": [{"id": 2202, "name": "Hemp-Based Performance Sound", "agent": "Simplified Patent Mining Agent"}, {"id": 2201, "name": "Hemp-Based Studio Sound", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.71, "desc_similarity": 0.94}, {"type": "near_duplicate", "products": [{"id": 2133, "name": "Hemp-Based Premium Packaging", "agent": "Simplified Patent Mining Agent"}, {"id": 2134, "name": "Hemp-Based Recycled Packaging", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.81, "desc_similarity": 0.96}, {"type": "near_duplicate", "products": [{"id": 2133, "name": "Hemp-Based Premium Packaging", "agent": "Simplified Patent Mining Agent"}, {"id": 2137, "name": "Hemp-Based Specialty Packaging", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.83, "desc_similarity": 0.95}, {"type": "near_duplicate", "products": [{"id": 2198, "name": "Hemp-Based Professional Sound", "agent": "Simplified Patent Mining Agent"}, {"id": 2201, "name": "Hemp-Based Studio Sound", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.77, "desc_similarity": 0.94}, {"type": "near_duplicate", "products": [{"id": 2160, "name": "Hemp-Based Protection Cleanup", "agent": "Simplified Patent Mining Agent"}, {"id": 2162, "name": "Hemp-Based Remediation Cleanup", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.85, "desc_similarity": 0.96}, {"type": "near_duplicate", "products": [{"id": 2134, "name": "Hemp-Based Recycled Packaging", "agent": "Simplified Patent Mining Agent"}, {"id": 2137, "name": "Hemp-Based Specialty Packaging", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.81, "desc_similarity": 0.96}, {"type": "near_duplicate", "products": [{"id": 189, "name": "Hemp-Based Supercapacitors", "agent": "None_quality_improvement"}, {"id": 190, "name": "Hemp-Based Ultracapacitors", "agent": "None_quality_improvement"}], "name_similarity": 0.88, "desc_similarity": 0.03}, {"type": "near_duplicate", "products": [{"id": 1400, "name": "Hemp Biochar Water Filter", "agent": "innovation_agent_quality_improvement"}, {"id": 1667, "name": "Hemp Root Biochar Water Filter", "agent": "None_quality_improvement"}], "name_similarity": 0.91, "desc_similarity": 1.0}, {"type": "near_duplicate", "products": [{"id": 269, "name": "Hemp Biocomposite", "agent": "None_quality_improvement"}, {"id": 201, "name": "Hemp Biocomposite Car Parts", "agent": "None_quality_improvement"}], "name_similarity": 0.77, "desc_similarity": 1.0}, {"type": "near_duplicate", "products": [{"id": 201, "name": "Hemp Biocomposite Car Parts", "agent": "None_quality_improvement"}, {"id": 245, "name": "Hemp Composite Car Panels", "agent": "None_quality_improvement"}], "name_similarity": 0.85, "desc_similarity": 1.0}, {"type": "near_duplicate", "products": [{"id": 201, "name": "Hemp Biocomposite Car Parts", "agent": "None_quality_improvement"}, {"id": 278, "name": "Hemp Composite Material", "agent": "None_quality_improvement"}], "name_similarity": 0.72, "desc_similarity": 1.0}, {"type": "near_duplicate", "products": [{"id": 180, "name": "Hemp Biocomposite Panels", "agent": "None_quality_improvement"}, {"id": 245, "name": "Hemp Composite Car Panels", "agent": "None_quality_improvement"}], "name_similarity": 0.86, "desc_similarity": 1.0}, {"type": "near_duplicate", "products": [{"id": 39, "name": "Hemp Bioplastic", "agent": "None_quality_improvement"}, {"id": 49, "name": "Hemp Bioplastic Packaging", "agent": "None_quality_improvement"}], "name_similarity": 0.75, "desc_similarity": 1.0}, {"type": "near_duplicate", "products": [{"id": 49, "name": "Hemp Bioplastic Packaging", "agent": "None_quality_improvement"}, {"id": 187, "name": "Hemp Bioplastic Packaging Film", "agent": "None_quality_improvement"}], "name_similarity": 0.91, "desc_similarity": 0.51}, {"type": "near_duplicate", "products": [{"id": 2054, "name": "Hemp Biopolymer Biodegradable Material", "agent": "Simplified Patent Mining Agent"}, {"id": 2057, "name": "Hemp Biopolymer Compostable Material", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.81, "desc_similarity": 0.95}, {"type": "near_duplicate", "products": [{"id": 2054, "name": "Hemp Biopolymer Biodegradable Material", "agent": "Simplified Patent Mining Agent"}, {"id": 2058, "name": "Hemp Biopolymer Flexible Material", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.82, "desc_similarity": 0.95}, {"type": "near_duplicate", "products": [{"id": 2057, "name": "Hemp Biopolymer Compostable Material", "agent": "Simplified Patent Mining Agent"}, {"id": 2058, "name": "Hemp Biopolymer Flexible Material", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.81, "desc_similarity": 0.96}, {"type": "near_duplicate", "products": [{"id": 272, "name": "<PERSON><PERSON>", "agent": "None_quality_improvement"}, {"id": 323, "name": "Hemp Geotextile", "agent": "None_quality_improvement"}], "name_similarity": 0.74, "desc_similarity": 1.0}, {"type": "near_duplicate", "products": [{"id": 22, "name": "Hemp Building Blocks", "agent": "None_quality_improvement"}, {"id": 1699, "name": "Hempcrete Building Blocks", "agent": "None_quality_improvement"}], "name_similarity": 0.89, "desc_similarity": 0.54}, {"type": "near_duplicate", "products": [{"id": 31, "name": "<PERSON><PERSON>", "agent": "None_quality_improvement"}, {"id": 27, "name": "<PERSON><PERSON>", "agent": "None_quality_improvement"}], "name_similarity": 0.76, "desc_similarity": 1.0}, {"type": "near_duplicate", "products": [{"id": 2048, "name": "Hemp Carbon Battery Electrode", "agent": "Simplified Patent Mining Agent"}, {"id": 2050, "name": "Hemp Carbon Electrode Electrode", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.8, "desc_similarity": 0.95}, {"type": "near_duplicate", "products": [{"id": 2048, "name": "Hemp Carbon Battery Electrode", "agent": "Simplified Patent Mining Agent"}, {"id": 2047, "name": "Hemp Carbon Supercapacitor Electrode", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.74, "desc_similarity": 0.93}, {"type": "near_duplicate", "products": [{"id": 318, "name": "Hemp Carbon Credits", "agent": "None_quality_improvement"}, {"id": 205, "name": "Hemp Carbon Fiber", "agent": "None_quality_improvement"}], "name_similarity": 0.72, "desc_similarity": 1.0}, {"type": "near_duplicate", "products": [{"id": 318, "name": "Hemp Carbon Credits", "agent": "None_quality_improvement"}, {"id": 276, "name": "Hemp Cardboard", "agent": "None_quality_improvement"}], "name_similarity": 0.73, "desc_similarity": 1.0}, {"type": "near_duplicate", "products": [{"id": 2050, "name": "Hemp Carbon Electrode Electrode", "agent": "Simplified Patent Mining Agent"}, {"id": 2047, "name": "Hemp Carbon Supercapacitor Electrode", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.78, "desc_similarity": 0.93}, {"type": "near_duplicate", "products": [{"id": 205, "name": "Hemp Carbon Fiber", "agent": "None_quality_improvement"}, {"id": 276, "name": "Hemp Cardboard", "agent": "None_quality_improvement"}], "name_similarity": 0.71, "desc_similarity": 1.0}, {"type": "near_duplicate", "products": [{"id": 205, "name": "Hemp Carbon Fiber", "agent": "None_quality_improvement"}, {"id": 335, "name": "Hemp Water Filter", "agent": "None_quality_improvement"}], "name_similarity": 0.71, "desc_similarity": 1.0}, {"type": "near_duplicate", "products": [{"id": 276, "name": "Hemp Cardboard", "agent": "None_quality_improvement"}, {"id": 282, "name": "Hemp Dashboard", "agent": "None_quality_improvement"}], "name_similarity": 0.79, "desc_similarity": 1.0}, {"type": "near_duplicate", "products": [{"id": 276, "name": "Hemp Cardboard", "agent": "None_quality_improvement"}, {"id": 331, "name": "<PERSON><PERSON>eboard", "agent": "None_quality_improvement"}], "name_similarity": 0.76, "desc_similarity": 1.0}, {"type": "near_duplicate", "products": [{"id": 276, "name": "Hemp Cardboard", "agent": "None_quality_improvement"}, {"id": 334, "name": "<PERSON>mp Surfboard", "agent": "None_quality_improvement"}], "name_similarity": 0.79, "desc_similarity": 1.0}, {"type": "near_duplicate", "products": [{"id": 38, "name": "Hemp Car Door Panels", "agent": "None_quality_improvement"}, {"id": 283, "name": "Hemp Door Panels", "agent": "None_quality_improvement"}], "name_similarity": 0.89, "desc_similarity": 1.0}, {"type": "near_duplicate", "products": [{"id": 1916, "name": "<PERSON><PERSON>", "agent": "turbo_generator_quality_improvement"}, {"id": 1387, "name": "<PERSON><PERSON>rd <PERSON>", "agent": "hurds_agent_quality_improvement"}], "name_similarity": 0.86, "desc_similarity": 1.0}, {"type": "near_duplicate", "products": [{"id": 277, "name": "<PERSON><PERSON>", "agent": "None_quality_improvement"}, {"id": 284, "name": "Hemp Ethanol", "agent": "None_quality_improvement"}], "name_similarity": 0.72, "desc_similarity": 1.0}, {"type": "near_duplicate", "products": [{"id": 2164, "name": "Hemp Cleanup Remediation Product", "agent": "Simplified Patent Mining Agent"}, {"id": 2163, "name": "Hemp Protection Remediation Product", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.81, "desc_similarity": 0.95}, {"type": "near_duplicate", "products": [{"id": 2164, "name": "Hemp Cleanup Remediation Product", "agent": "Simplified Patent Mining Agent"}, {"id": 2159, "name": "Hemp Remediation Remediation Product", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.82, "desc_similarity": 0.94}, {"type": "near_duplicate", "products": [{"id": 2211, "name": "Hemp Comfort Pet Product", "agent": "Simplified Patent Mining Agent"}, {"id": 2205, "name": "Hemp Health Pet Product", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.77, "desc_similarity": 0.96}, {"type": "near_duplicate", "products": [{"id": 2150, "name": "Hemp Component Electronic Component", "agent": "Simplified Patent Mining Agent"}, {"id": 2154, "name": "Hemp Module Electronic Component", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.84, "desc_similarity": 0.95}, {"type": "near_duplicate", "products": [{"id": 2150, "name": "Hemp Component Electronic Component", "agent": "Simplified Patent Mining Agent"}, {"id": 2158, "name": "Hemp System Electronic Component", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.81, "desc_similarity": 0.83}, {"type": "near_duplicate", "products": [{"id": 245, "name": "Hemp Composite Car Panels", "agent": "None_quality_improvement"}, {"id": 278, "name": "Hemp Composite Material", "agent": "None_quality_improvement"}], "name_similarity": 0.79, "desc_similarity": 1.0}, {"type": "near_duplicate", "products": [{"id": 245, "name": "Hemp Composite Car Panels", "agent": "None_quality_improvement"}, {"id": 283, "name": "Hemp Door Panels", "agent": "None_quality_improvement"}], "name_similarity": 0.73, "desc_similarity": 1.0}, {"type": "near_duplicate", "products": [{"id": 1923, "name": "Hemp Composite Decking", "agent": "turbo_generator_quality_improvement"}, {"id": 1686, "name": "Hemp Shiv Composite Decking", "agent": "None_quality_improvement"}], "name_similarity": 0.9, "desc_similarity": 0.47}, {"type": "near_duplicate", "products": [{"id": 320, "name": "Hemp Concrete Blocks", "agent": "None_quality_improvement"}, {"id": 81, "name": "Hempcrete Block", "agent": "None_quality_improvement"}], "name_similarity": 0.86, "desc_similarity": 0.03}, {"type": "near_duplicate", "products": [{"id": 246, "name": "Hemp Copy Paper", "agent": "None_quality_improvement"}, {"id": 292, "name": "Hemp Paper", "agent": "None_quality_improvement"}], "name_similarity": 0.8, "desc_similarity": 1.0}, {"type": "near_duplicate", "products": [{"id": 282, "name": "Hemp Dashboard", "agent": "None_quality_improvement"}, {"id": 331, "name": "<PERSON><PERSON>eboard", "agent": "None_quality_improvement"}], "name_similarity": 0.76, "desc_similarity": 1.0}, {"type": "near_duplicate", "products": [{"id": 282, "name": "Hemp Dashboard", "agent": "None_quality_improvement"}, {"id": 334, "name": "<PERSON>mp Surfboard", "agent": "None_quality_improvement"}], "name_similarity": 0.79, "desc_similarity": 1.0}, {"type": "near_duplicate", "products": [{"id": 15, "name": "<PERSON><PERSON>", "agent": "None_quality_improvement"}, {"id": 59, "name": "<PERSON><PERSON>", "agent": "None_quality_improvement"}], "name_similarity": 0.77, "desc_similarity": 1.0}, {"type": "near_duplicate", "products": [{"id": 321, "name": "<PERSON><PERSON>", "agent": "None_quality_improvement"}, {"id": 283, "name": "Hemp Door Panels", "agent": "None_quality_improvement"}], "name_similarity": 0.73, "desc_similarity": 1.0}, {"type": "near_duplicate", "products": [{"id": 2248, "name": "<PERSON><PERSON>-Derived Attenuates Compound", "agent": "Academic Research Agent"}, {"id": 2249, "name": "<PERSON><PERSON>-Derived <PERSON><PERSON><PERSON>", "agent": "Academic Research Agent"}], "name_similarity": 0.73, "desc_similarity": 0.94}, {"type": "near_duplicate", "products": [{"id": 2049, "name": "Hemp-Derived Battery Battery Component", "agent": "Simplified Patent Mining Agent"}, {"id": 2081, "name": "Hemp-Derived Electrode Battery Component", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.85, "desc_similarity": 0.95}, {"type": "near_duplicate", "products": [{"id": 2049, "name": "Hemp-Derived Battery Battery Component", "agent": "Simplified Patent Mining Agent"}, {"id": 2044, "name": "Hemp-Derived Supercapacitor Battery Component", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.8, "desc_similarity": 0.93}, {"type": "near_duplicate", "products": [{"id": 2064, "name": "Hemp-Derived Carbon Nanomaterial", "agent": "Simplified Patent Mining Agent"}, {"id": 2067, "name": "<PERSON>mp-Derived Graphene Nanomater<PERSON>", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.85, "desc_similarity": 0.96}, {"type": "near_duplicate", "products": [{"id": 2064, "name": "Hemp-Derived Carbon Nanomaterial", "agent": "Simplified Patent Mining Agent"}, {"id": 2090, "name": "Hemp-Derived Quantum Dot Nanomaterial", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.81, "desc_similarity": 0.94}, {"type": "near_duplicate", "products": [{"id": 2081, "name": "Hemp-Derived Electrode Battery Component", "agent": "Simplified Patent Mining Agent"}, {"id": 2044, "name": "Hemp-Derived Supercapacitor Battery Component", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.82, "desc_similarity": 0.93}, {"type": "near_duplicate", "products": [{"id": 2067, "name": "<PERSON>mp-Derived Graphene Nanomater<PERSON>", "agent": "Simplified Patent Mining Agent"}, {"id": 2090, "name": "Hemp-Derived Quantum Dot Nanomaterial", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.79, "desc_similarity": 0.94}, {"type": "near_duplicate", "products": [{"id": 2195, "name": "Hemp Emergency Safety Product", "agent": "Simplified Patent Mining Agent"}, {"id": 2197, "name": "Hemp Prevention Safety Product", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.78, "desc_similarity": 0.95}, {"type": "near_duplicate", "products": [{"id": 2195, "name": "Hemp Emergency Safety Product", "agent": "Simplified Patent Mining Agent"}, {"id": 2193, "name": "Hemp Protection Safety Product", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.75, "desc_similarity": 0.95}, {"type": "near_duplicate", "products": [{"id": 5, "name": "Hemp Energy Bars", "agent": "None_quality_improvement"}, {"id": 1374, "name": "Hemp Seed Energy Bars", "agent": "seeds_agent_quality_improvement"}], "name_similarity": 0.86, "desc_similarity": 1.0}, {"type": "near_duplicate", "products": [{"id": 2122, "name": "Hemp Exterior Automotive Component", "agent": "Simplified Patent Mining Agent"}, {"id": 2104, "name": "Hemp Interior Automotive Component", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.94, "desc_similarity": 0.81}, {"type": "near_duplicate", "products": [{"id": 86, "name": "Hemp Extract Capsules", "agent": "None_quality_improvement"}, {"id": 119, "name": "Hemp Root Extract Capsules", "agent": "None_quality_improvement"}], "name_similarity": 0.89, "desc_similarity": 0.15}, {"type": "near_duplicate", "products": [{"id": 2106, "name": "Hemp Fiber Air Membrane", "agent": "Simplified Patent Mining Agent"}, {"id": 2252, "name": "Hemp Fiber Industrial Membrane", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.79, "desc_similarity": 0.95}, {"type": "near_duplicate", "products": [{"id": 1709, "name": "Hemp Fiber Fiber Insulation Panels", "agent": "None_quality_improvement"}, {"id": 1665, "name": "Hemp Root Fiber Insulation Panels", "agent": "None_quality_improvement"}], "name_similarity": 0.9, "desc_similarity": 0.74}, {"type": "near_duplicate", "products": [{"id": 91, "name": "Hemp Fiber Insulation", "agent": "None_quality_improvement"}, {"id": 177, "name": "Hemp Fiber Insulation Batts", "agent": "None_quality_improvement"}], "name_similarity": 0.88, "desc_similarity": 0.21}, {"type": "near_duplicate", "products": [{"id": 91, "name": "Hemp Fiber Insulation", "agent": "None_quality_improvement"}, {"id": 1831, "name": "Hemp Fiber Insulation Board", "agent": "turbo_generator_quality_improvement"}], "name_similarity": 0.88, "desc_similarity": 0.21}, {"type": "near_duplicate", "products": [{"id": 177, "name": "Hemp Fiber Insulation Batts", "agent": "None_quality_improvement"}, {"id": 1831, "name": "Hemp Fiber Insulation Board", "agent": "turbo_generator_quality_improvement"}], "name_similarity": 0.89, "desc_similarity": 1.0}, {"type": "near_duplicate", "products": [{"id": 177, "name": "Hemp Fiber Insulation Batts", "agent": "None_quality_improvement"}, {"id": 1698, "name": "Hemp Hurd Insulation Panels", "agent": "None_quality_improvement"}], "name_similarity": 0.74, "desc_similarity": 0.82}, {"type": "near_duplicate", "products": [{"id": 177, "name": "Hemp Fiber Insulation Batts", "agent": "None_quality_improvement"}, {"id": 1, "name": "<PERSON><PERSON> Insul<PERSON><PERSON>", "agent": "None_quality_improvement"}], "name_similarity": 0.88, "desc_similarity": 1.0}, {"type": "near_duplicate", "products": [{"id": 2076, "name": "Hemp Fiber Lightweight Composite Material", "agent": "Simplified Patent Mining Agent"}, {"id": 2042, "name": "Hemp Fiber Structural Composite Material", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.77, "desc_similarity": 0.94}, {"type": "near_duplicate", "products": [{"id": 286, "name": "Hemp <PERSON>lour", "agent": "None_quality_improvement"}, {"id": 69, "name": "<PERSON><PERSON>", "agent": "None_quality_improvement"}], "name_similarity": 0.86, "desc_similarity": 0.03}, {"type": "near_duplicate", "products": [{"id": 2098, "name": "Hemp Flour Food Product", "agent": "Simplified Patent Mining Agent"}, {"id": 2092, "name": "Hemp Oil Food Product", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.86, "desc_similarity": 0.97}, {"type": "near_duplicate", "products": [{"id": 2098, "name": "Hemp Flour Food Product", "agent": "Simplified Patent Mining Agent"}, {"id": 2096, "name": "Hemp Protein Food Product", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.79, "desc_similarity": 0.96}, {"type": "near_duplicate", "products": [{"id": 183, "name": "Hemp Flower Extract", "agent": "None_quality_improvement"}, {"id": 1385, "name": "Hemp Flower Pet Tincture", "agent": "flowers_agent_quality_improvement"}], "name_similarity": 0.74, "desc_similarity": 1.0}, {"type": "near_duplicate", "products": [{"id": 1383, "name": "<PERSON><PERSON> Face Cream", "agent": "flowers_agent_quality_improvement"}, {"id": 1382, "name": "He<PERSON> Flower Vape Cartridge", "agent": "flowers_agent_quality_improvement"}], "name_similarity": 0.75, "desc_similarity": 1.0}, {"type": "near_duplicate", "products": [{"id": 1385, "name": "Hemp Flower Pet Tincture", "agent": "flowers_agent_quality_improvement"}, {"id": 299, "name": "Hemp Tincture", "agent": "None_quality_improvement"}], "name_similarity": 0.7, "desc_similarity": 1.0}, {"type": "near_duplicate", "products": [{"id": 287, "name": "Hempful Farms Organic Hemp Seed Snack", "agent": "None_quality_improvement"}, {"id": 93, "name": "Organic Hemp Seed Snack", "agent": "None_quality_improvement"}], "name_similarity": 0.77, "desc_similarity": 1.0}, {"type": "near_duplicate", "products": [{"id": 323, "name": "Hemp Geotextile", "agent": "None_quality_improvement"}, {"id": 1922, "name": "Hemp Geotextiles", "agent": "turbo_generator_quality_improvement"}], "name_similarity": 0.97, "desc_similarity": 0.2}, {"type": "near_duplicate", "products": [{"id": 1394, "name": "Hemp Graphene Supercapacitor", "agent": "innovation_agent_quality_improvement"}, {"id": 333, "name": "Hemp Supercapacitor", "agent": "None_quality_improvement"}], "name_similarity": 0.81, "desc_similarity": 1.0}, {"type": "near_duplicate", "products": [{"id": 2149, "name": "Hemp Growth Agricultural Product", "agent": "Simplified Patent Mining Agent"}, {"id": 2147, "name": "Hemp Protection Agricultural Product", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.85, "desc_similarity": 0.56}, {"type": "near_duplicate", "products": [{"id": 2127, "name": "Hemp Haircare Beauty Product", "agent": "Simplified Patent Mining Agent"}, {"id": 2131, "name": "Hemp Skincare Beauty Product", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.89, "desc_similarity": 0.81}, {"type": "near_duplicate", "products": [{"id": 11, "name": "Hemp Hearts/Hulled Hemp Seeds", "agent": "None_quality_improvement"}, {"id": 28, "name": "<PERSON><PERSON> (Shelled Hemp Seeds)", "agent": "None_quality_improvement"}], "name_similarity": 0.89, "desc_similarity": 0.16}, {"type": "near_duplicate", "products": [{"id": 1658, "name": "<PERSON><PERSON>rd Acoustic Panels", "agent": "None_quality_improvement"}, {"id": 1698, "name": "Hemp Hurd Insulation Panels", "agent": "None_quality_improvement"}], "name_similarity": 0.77, "desc_similarity": 1.0}, {"type": "near_duplicate", "products": [{"id": 1657, "name": "<PERSON><PERSON> Hurd Animal Bedding Pellets", "agent": "None_quality_improvement"}, {"id": 1691, "name": "Hemp Shiv Animal Bedding", "agent": "None_quality_improvement"}], "name_similarity": 0.75, "desc_similarity": 1.0}, {"type": "near_duplicate", "products": [{"id": 1388, "name": "<PERSON><PERSON>rd Growing Medium", "agent": "hurds_agent_quality_improvement"}, {"id": 1719, "name": "Hemp Hurd Hydroponic Grow Medium", "agent": "None_quality_improvement"}], "name_similarity": 0.75, "desc_similarity": 1.0}, {"type": "near_duplicate", "products": [{"id": 1920, "name": "Hemp Hurd Particle Board", "agent": "turbo_generator_quality_improvement"}, {"id": 178, "name": "Hemp Particle Board", "agent": "None_quality_improvement"}], "name_similarity": 0.88, "desc_similarity": 1.0}, {"type": "near_duplicate", "products": [{"id": 19, "name": "Hemp Insulation", "agent": "None_quality_improvement"}, {"id": 1, "name": "<PERSON><PERSON> Insul<PERSON><PERSON>", "agent": "None_quality_improvement"}], "name_similarity": 0.83, "desc_similarity": 1.0}, {"type": "near_duplicate", "products": [{"id": 19, "name": "Hemp Insulation", "agent": "None_quality_improvement"}, {"id": 168, "name": "HempWool Insulation", "agent": "None_quality_improvement"}], "name_similarity": 0.88, "desc_similarity": 1.0}, {"type": "near_duplicate", "products": [{"id": 1363, "name": "Hemp Leaf Animal Feed Supplement", "agent": "leaves_agent_quality_improvement"}, {"id": 1369, "name": "Hemp Leaf Pet Supplement", "agent": "leaves_agent_quality_improvement"}], "name_similarity": 0.79, "desc_similarity": 1.0}, {"type": "near_duplicate", "products": [{"id": 1708, "name": "Hemp Leaf Extract Sleep Tincture", "agent": "None_quality_improvement"}, {"id": 98, "name": "Hemp Leaf Extract Tincture", "agent": "None_quality_improvement"}], "name_similarity": 0.9, "desc_similarity": 0.01}, {"type": "near_duplicate", "products": [{"id": 1359, "name": "Hemp Leaf Green Powder", "agent": "leaves_agent_quality_improvement"}, {"id": 1855, "name": "Hemp Leaf <PERSON>tein <PERSON>", "agent": "turbo_generator_quality_improvement"}], "name_similarity": 0.87, "desc_similarity": 0.22}, {"type": "near_duplicate", "products": [{"id": 1855, "name": "Hemp Leaf <PERSON>tein <PERSON>", "agent": "turbo_generator_quality_improvement"}, {"id": 26, "name": "<PERSON><PERSON>", "agent": "None_quality_improvement"}], "name_similarity": 0.88, "desc_similarity": 0.14}, {"type": "near_duplicate", "products": [{"id": 1855, "name": "Hemp Leaf <PERSON>tein <PERSON>", "agent": "turbo_generator_quality_improvement"}, {"id": 188, "name": "<PERSON><PERSON>d <PERSON>", "agent": "None_quality_improvement"}], "name_similarity": 0.88, "desc_similarity": 0.14}, {"type": "near_duplicate", "products": [{"id": 2154, "name": "Hemp Module Electronic Component", "agent": "Simplified Patent Mining Agent"}, {"id": 2158, "name": "Hemp System Electronic Component", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.84, "desc_similarity": 0.84}, {"type": "near_duplicate", "products": [{"id": 2084, "name": "Hemp NanoCarbon Product", "agent": "Simplified Patent Mining Agent"}, {"id": 2063, "name": "Hemp NanoGraphene Product", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.79, "desc_similarity": 0.96}, {"type": "near_duplicate", "products": [{"id": 2084, "name": "Hemp NanoCarbon Product", "agent": "Simplified Patent Mining Agent"}, {"id": 2065, "name": "Hemp NanoQuantum Dot Product", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.75, "desc_similarity": 0.94}, {"type": "near_duplicate", "products": [{"id": 1397, "name": "<PERSON><PERSON> Wound Dressing", "agent": "innovation_agent_quality_improvement"}, {"id": 336, "name": "<PERSON><PERSON> Wound Dressing", "agent": "None_quality_improvement"}], "name_similarity": 0.79, "desc_similarity": 1.0}, {"type": "near_duplicate", "products": [{"id": 2063, "name": "Hemp NanoGraphene Product", "agent": "Simplified Patent Mining Agent"}, {"id": 2065, "name": "Hemp NanoQuantum Dot Product", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.72, "desc_similarity": 0.94}, {"type": "near_duplicate", "products": [{"id": 2092, "name": "Hemp Oil Food Product", "agent": "Simplified Patent Mining Agent"}, {"id": 2096, "name": "Hemp Protein Food Product", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.87, "desc_similarity": 0.96}, {"type": "near_duplicate", "products": [{"id": 7, "name": "Hemp Oil Shampoo", "agent": "None_quality_improvement"}, {"id": 297, "name": "<PERSON><PERSON>poo", "agent": "None_quality_improvement"}], "name_similarity": 0.86, "desc_similarity": 0.22}, {"type": "near_duplicate", "products": [{"id": 2144, "name": "Hemp Organic Agricultural Product", "agent": "Simplified Patent Mining Agent"}, {"id": 2147, "name": "Hemp Protection Agricultural Product", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.81, "desc_similarity": 0.95}, {"type": "near_duplicate", "products": [{"id": 292, "name": "Hemp Paper", "agent": "None_quality_improvement"}, {"id": 300, "name": "Hemp <PERSON>ue Paper", "agent": "None_quality_improvement"}], "name_similarity": 0.74, "desc_similarity": 1.0}, {"type": "near_duplicate", "products": [{"id": 45, "name": "<PERSON><PERSON> Bedding", "agent": "None_quality_improvement"}, {"id": 2030, "name": "Hemp Shiv <PERSON>", "agent": "None_quality_improvement"}], "name_similarity": 0.86, "desc_similarity": 1.0}, {"type": "near_duplicate", "products": [{"id": 2197, "name": "Hemp Prevention Safety Product", "agent": "Simplified Patent Mining Agent"}, {"id": 2193, "name": "Hemp Protection Safety Product", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.9, "desc_similarity": 0.98}, {"type": "near_duplicate", "products": [{"id": 2168, "name": "Hemp Professional Sports Equipment", "agent": "Simplified Patent Mining Agent"}, {"id": 2173, "name": "Hemp Recreational Sports Equipment", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.85, "desc_similarity": 0.86}, {"type": "near_duplicate", "products": [{"id": 2163, "name": "Hemp Protection Remediation Product", "agent": "Simplified Patent Mining Agent"}, {"id": 2159, "name": "Hemp Remediation Remediation Product", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.87, "desc_similarity": 0.96}, {"type": "near_duplicate", "products": [{"id": 204, "name": "Hemp Protein Meat Alternatives", "agent": "None_quality_improvement"}, {"id": 1405, "name": "Hemp Protein Meat Scaffold", "agent": "innovation_agent_quality_improvement"}], "name_similarity": 0.71, "desc_similarity": 1.0}, {"type": "near_duplicate", "products": [{"id": 26, "name": "<PERSON><PERSON>", "agent": "None_quality_improvement"}, {"id": 242, "name": "<PERSON><PERSON> 50%", "agent": "None_quality_improvement"}], "name_similarity": 0.9, "desc_similarity": 0.19}, {"type": "near_duplicate", "products": [{"id": 26, "name": "<PERSON><PERSON>", "agent": "None_quality_improvement"}, {"id": 1934, "name": "<PERSON><PERSON> Powder", "agent": "turbo_generator_quality_improvement"}], "name_similarity": 0.86, "desc_similarity": 0.19}, {"type": "near_duplicate", "products": [{"id": 26, "name": "<PERSON><PERSON>", "agent": "None_quality_improvement"}, {"id": 188, "name": "<PERSON><PERSON>d <PERSON>", "agent": "None_quality_improvement"}], "name_similarity": 0.88, "desc_similarity": 1.0}, {"type": "near_duplicate", "products": [{"id": 1931, "name": "<PERSON><PERSON>", "agent": "turbo_generator_quality_improvement"}, {"id": 1932, "name": "Hemp Root Tea", "agent": "turbo_generator_quality_improvement"}], "name_similarity": 0.81, "desc_similarity": 1.0}, {"type": "near_duplicate", "products": [{"id": 261, "name": "hemp Sativa Seed Oil Cleansing Facial Oil", "agent": "None_quality_improvement"}, {"id": 70, "name": "Seed Oil Cleansing Facial Oil", "agent": "None_quality_improvement"}], "name_similarity": 0.83, "desc_similarity": 1.0}, {"type": "near_duplicate", "products": [{"id": 243, "name": "<PERSON><PERSON> Seed Face Moisturizer", "agent": "None_quality_improvement"}, {"id": 128, "name": "Hemp Seed Oil Facial Moisturizer", "agent": "None_quality_improvement"}], "name_similarity": 0.86, "desc_similarity": 0.04}, {"type": "near_duplicate", "products": [{"id": 1370, "name": "Hemp Seed Protein Isolate", "agent": "seeds_agent_quality_improvement"}, {"id": 1676, "name": "HempSeedProteinIsolates™", "agent": "None_quality_improvement"}], "name_similarity": 0.9, "desc_similarity": 0.01}, {"type": "near_duplicate", "products": [{"id": 297, "name": "<PERSON><PERSON>poo", "agent": "None_quality_improvement"}, {"id": 298, "name": "Hemp <PERSON>", "agent": "None_quality_improvement"}], "name_similarity": 0.76, "desc_similarity": 1.0}, {"type": "near_duplicate", "products": [{"id": 1691, "name": "Hemp Shiv Animal Bedding", "agent": "None_quality_improvement"}, {"id": 2030, "name": "Hemp Shiv <PERSON>", "agent": "None_quality_improvement"}], "name_similarity": 0.8, "desc_similarity": 1.0}, {"type": "near_duplicate", "products": [{"id": 331, "name": "<PERSON><PERSON>eboard", "agent": "None_quality_improvement"}, {"id": 334, "name": "<PERSON>mp Surfboard", "agent": "None_quality_improvement"}], "name_similarity": 0.76, "desc_similarity": 1.0}, {"type": "near_duplicate", "products": [{"id": 2243, "name": "Hemp S  Potential Application", "agent": "Academic Research Agent"}, {"id": 2250, "name": "Hemp The  Potential Application", "agent": "Academic Research Agent"}], "name_similarity": 0.93, "desc_similarity": 0.45}, {"type": "near_duplicate", "products": [{"id": 336, "name": "<PERSON><PERSON> Wound Dressing", "agent": "None_quality_improvement"}, {"id": 1894, "name": "<PERSON><PERSON> Wound Dressing Film", "agent": "unique_generator_quality_improvement"}], "name_similarity": 0.88, "desc_similarity": 0.03}, {"type": "near_duplicate", "products": [{"id": 303, "name": "Hempzilla CBD Infused Hemp Seed Oil", "agent": "None_quality_improvement"}, {"id": 139, "name": "Infused Hemp Seed Oil", "agent": "None_quality_improvement"}], "name_similarity": 0.75, "desc_similarity": 1.0}, {"type": "near_duplicate", "products": [{"id": 2037, "name": "High-Performance Hemp Flower Terpene Complex - Stress Relief", "agent": "working_product_adder_v2_quality_improvement"}, {"id": 1956, "name": "Professional Hemp Flower Terpene Complex - Stress Relief", "agent": "working_product_adder_v2_quality_improvement"}], "name_similarity": 0.81, "desc_similarity": 1.0}, {"type": "near_duplicate", "products": [{"id": 2080, "name": "High-Strength Hemp Lightweight Composite", "agent": "Simplified Patent Mining Agent"}, {"id": 2077, "name": "High-Strength Hemp Reinforced Composite", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.78, "desc_similarity": 0.94}, {"type": "near_duplicate", "products": [{"id": 2080, "name": "High-Strength Hemp Lightweight Composite", "agent": "Simplified Patent Mining Agent"}, {"id": 2079, "name": "High-Strength Hemp Structural Composite", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.76, "desc_similarity": 0.94}, {"type": "near_duplicate", "products": [{"id": 2077, "name": "High-Strength Hemp Reinforced Composite", "agent": "Simplified Patent Mining Agent"}, {"id": 2079, "name": "High-Strength Hemp Structural Composite", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.79, "desc_similarity": 0.95}, {"type": "near_duplicate", "products": [{"id": 2176, "name": "Maritime Hemp Commercial Material", "agent": "Simplified Patent Mining Agent"}, {"id": 2179, "name": "Maritime Hemp Recreational Material", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.79, "desc_similarity": 0.95}, {"type": "near_duplicate", "products": [{"id": 2176, "name": "Maritime Hemp Commercial Material", "agent": "Simplified Patent Mining Agent"}, {"id": 2182, "name": "Maritime Hemp Safety Material", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.77, "desc_similarity": 0.87}, {"type": "near_duplicate", "products": [{"id": 2179, "name": "Maritime Hemp Recreational Material", "agent": "Simplified Patent Mining Agent"}, {"id": 2182, "name": "Maritime Hemp Safety Material", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.78, "desc_similarity": 0.87}, {"type": "near_duplicate", "products": [{"id": 2101, "name": "Medical Hemp Capsule Product", "agent": "Simplified Patent Mining Agent"}, {"id": 2099, "name": "Medical Hemp Delivery System Product", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.72, "desc_similarity": 0.93}, {"type": "near_duplicate", "products": [{"id": 2101, "name": "Medical Hemp Capsule Product", "agent": "Simplified Patent Mining Agent"}, {"id": 2073, "name": "Medical Hemp Formulation Product", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.77, "desc_similarity": 0.94}, {"type": "near_duplicate", "products": [{"id": 2132, "name": "Natural Hemp Anti-Aging Care", "agent": "Simplified Patent Mining Agent"}, {"id": 2129, "name": "Natural Hemp Haircare Care", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.78, "desc_similarity": 0.82}, {"type": "near_duplicate", "products": [{"id": 2209, "name": "Natural Hemp Comfort Pet Care", "agent": "Simplified Patent Mining Agent"}, {"id": 2207, "name": "Natural Hemp Training Pet Care", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.78, "desc_similarity": 0.96}, {"type": "near_duplicate", "products": [{"id": 2190, "name": "Natural Hemp Flexible Bonding", "agent": "Simplified Patent Mining Agent"}, {"id": 2189, "name": "Natural Hemp Water-Based Bonding", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.79, "desc_similarity": 0.94}, {"type": "near_duplicate", "products": [{"id": 2129, "name": "Natural Hemp Haircare Care", "agent": "Simplified Patent Mining Agent"}, {"id": 2126, "name": "Natural Hemp Skincare Care", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.88, "desc_similarity": 0.83}, {"type": "near_duplicate", "products": [{"id": 305, "name": "Nature’s Path Organic Hemp Plus Granola", "agent": "None_quality_improvement"}, {"id": 141, "name": "Organic Hemp Plus Granola", "agent": "None_quality_improvement"}], "name_similarity": 0.78, "desc_similarity": 1.0}, {"type": "near_duplicate", "products": [{"id": 2093, "name": "Nutritional Hemp Flour Supplement", "agent": "Simplified Patent Mining Agent"}, {"id": 2095, "name": "Nutritional Hemp Oil Supplement", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.91, "desc_similarity": 0.97}, {"type": "near_duplicate", "products": [{"id": 2093, "name": "Nutritional Hemp Flour Supplement", "agent": "Simplified Patent Mining Agent"}, {"id": 2091, "name": "Nutritional Hemp Protein Supplement", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.85, "desc_similarity": 0.96}, {"type": "near_duplicate", "products": [{"id": 2095, "name": "Nutritional Hemp Oil Supplement", "agent": "Simplified Patent Mining Agent"}, {"id": 2091, "name": "Nutritional Hemp Protein Supplement", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.91, "desc_similarity": 0.96}, {"type": "near_duplicate", "products": [{"id": 146, "name": "Organic Hemp Flower", "agent": "None_quality_improvement"}, {"id": 310, "name": "Secret Nature Organic Hemp Flower", "agent": "None_quality_improvement"}], "name_similarity": 0.73, "desc_similarity": 1.0}, {"type": "near_duplicate", "products": [{"id": 2142, "name": "Organic Hemp Growth Solution", "agent": "Simplified Patent Mining Agent"}, {"id": 2141, "name": "Organic Hemp Organic Solution", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.81, "desc_similarity": 0.96}, {"type": "near_duplicate", "products": [{"id": 35, "name": "Organic Hemp Hearts", "agent": null}, {"id": 143, "name": "Organic Hemp Seed Hearts", "agent": "None_quality_improvement"}], "name_similarity": 0.88, "desc_similarity": 0.1}, {"type": "near_duplicate", "products": [{"id": 2171, "name": "Performance Hemp Professional Product", "agent": "Simplified Patent Mining Agent"}, {"id": 2169, "name": "Performance Hemp Recreational Product", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.86, "desc_similarity": 0.88}, {"type": "near_duplicate", "products": [{"id": 2119, "name": "Premium Hemp Blended Textile", "agent": "Simplified Patent Mining Agent"}, {"id": 2060, "name": "Premium Hemp Woven Textile", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.85, "desc_similarity": 0.33}, {"type": "near_duplicate", "products": [{"id": 2078, "name": "Reinforced Hemp Lightweight Panel", "agent": "Simplified Patent Mining Agent"}, {"id": 2041, "name": "Reinforced Hemp Reinforced Panel", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.74, "desc_similarity": 0.94}, {"type": "near_duplicate", "products": [{"id": 2078, "name": "Reinforced Hemp Lightweight Panel", "agent": "Simplified Patent Mining Agent"}, {"id": 2043, "name": "Reinforced Hemp Structural Panel", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.71, "desc_similarity": 0.94}, {"type": "near_duplicate", "products": [{"id": 2041, "name": "Reinforced Hemp Reinforced Panel", "agent": "Simplified Patent Mining Agent"}, {"id": 2043, "name": "Reinforced Hemp Structural Panel", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.75, "desc_similarity": 0.95}, {"type": "near_duplicate", "products": [{"id": 2082, "name": "Structural Hemp Insulation Product", "agent": "Simplified Patent Mining Agent"}, {"id": 2053, "name": "Structural Hemp Structural Product", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.79, "desc_similarity": 0.95}, {"type": "near_duplicate", "products": [{"id": 2157, "name": "Sustainable Component Electronics", "agent": "Simplified Patent Mining Agent"}, {"id": 2156, "name": "Sustainable Module Electronics", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.83, "desc_similarity": 0.81}, {"type": "near_duplicate", "products": [{"id": 2157, "name": "Sustainable Component Electronics", "agent": "Simplified Patent Mining Agent"}, {"id": 2153, "name": "Sustainable System Electronics", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.79, "desc_similarity": 0.81}, {"type": "near_duplicate", "products": [{"id": 2059, "name": "Sustainable Hemp Biodegradable Packaging", "agent": "Simplified Patent Mining Agent"}, {"id": 2083, "name": "Sustainable Hemp Compostable Packaging", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.82, "desc_similarity": 0.95}, {"type": "near_duplicate", "products": [{"id": 2059, "name": "Sustainable Hemp Biodegradable Packaging", "agent": "Simplified Patent Mining Agent"}, {"id": 2056, "name": "Sustainable Hemp Flexible Packaging", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.83, "desc_similarity": 0.95}, {"type": "near_duplicate", "products": [{"id": 2083, "name": "Sustainable Hemp Compostable Packaging", "agent": "Simplified Patent Mining Agent"}, {"id": 2056, "name": "Sustainable Hemp Flexible Packaging", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.82, "desc_similarity": 0.96}, {"type": "near_duplicate", "products": [{"id": 2136, "name": "Sustainable Hemp Recycled Paper", "agent": "Simplified Patent Mining Agent"}, {"id": 2140, "name": "Sustainable Hemp Specialty Paper", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.83, "desc_similarity": 0.83}, {"type": "near_duplicate", "products": [{"id": 2156, "name": "Sustainable Module Electronics", "agent": "Simplified Patent Mining Agent"}, {"id": 2153, "name": "Sustainable System Electronics", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.83, "desc_similarity": 0.97}, {"type": "near_duplicate", "products": [{"id": 2103, "name": "Vehicle Hemp Exterior Part", "agent": "Simplified Patent Mining Agent"}, {"id": 2121, "name": "Vehicle Hemp Interior Part", "agent": "Simplified Patent Mining Agent"}], "name_similarity": 0.92, "desc_similarity": 0.51}], "low_quality": [], "cannabis_naming": [{"id": 1994, "name": "THC-Free Focus Tincture", "issues": ["name contains 'thc'"], "agent": "None_quality_improvement", "created": "2025-07-05T00:41:04.714006"}, {"id": 1969, "name": "THC-Free CBD Coffee Pods", "issues": ["name contains 'thc'", "description contains 'psychoactive'"], "agent": "None_quality_improvement", "created": "2025-07-04T22:35:59.574378"}, {"id": 1982, "name": "Delta-8 THC Sleep Gummies", "issues": ["name contains 'thc'", "description contains 'thc'"], "agent": "None_quality_improvement", "created": "2025-07-04T23:38:58.847297"}, {"id": 1697, "name": "Hemp Flower Smoking Blend", "issues": ["description contains 'psychoactive'"], "agent": "None_quality_improvement", "created": "2025-07-04T08:55:53.565134"}, {"id": 1559, "name": "HempRoot Hydrating Body Balm with CBG Microspheres", "issues": ["description contains 'thc'", "description contains 'psychoactive'"], "agent": "ai_discovery_agent_quality_improvement", "created": "2025-07-03T09:00:58.900869"}, {"id": 1725, "name": "Hemp Flower Pet Calming Chews", "issues": ["description contains 'psychoactive'"], "agent": "None_quality_improvement", "created": "2025-07-04T12:38:17.865077"}, {"id": 1431, "name": "THCV Energy Gummies", "issues": ["name contains 'thc'"], "agent": "cannabinoids_agent_quality_improvement", "created": "2025-07-03T03:02:55.928703"}, {"id": 2229, "name": "Hemp-Derived Can<PERSON>bis  To Compound", "issues": ["description contains 'thc'"], "agent": "Academic Research Agent", "created": "2025-07-08T20:54:58.903190"}, {"id": 2230, "name": "Hemp And  Restrictions Formulation", "issues": ["description contains 'thc'"], "agent": "Academic Research Agent", "created": "2025-07-08T20:54:59.359374"}, {"id": 2233, "name": "Hemp-Derived A  Source Compound", "issues": ["description contains 'thc'"], "agent": "Academic Research Agent", "created": "2025-07-08T20:55:05.097471"}, {"id": 264, "name": "Eureka Vapor CBD Hemp Flower", "issues": ["description contains 'thc'", "description contains 'psychoactive'"], "agent": "None_quality_improvement", "created": "2025-06-06T08:16:55.866631"}, {"id": 1716, "name": "Delta-8 Hemp Extract Vegan Gummies", "issues": ["description contains 'psychoactive'"], "agent": "None_quality_improvement", "created": "2025-07-04T10:00:30.308831"}, {"id": 2240, "name": "Hemp-Based Cellulose  Of Material", "issues": ["description contains 'thc'"], "agent": "Academic Research Agent", "created": "2025-07-08T20:55:16.201521"}, {"id": 2241, "name": "Hemp-Based Fiber Material", "issues": ["description contains 'thc'"], "agent": "Academic Research Agent", "created": "2025-07-08T20:55:16.918572"}, {"id": 2144, "name": "Hemp Organic Agricultural Product", "issues": ["description contains 'weed'"], "agent": "Simplified Patent Mining Agent", "created": "2025-07-08T20:52:56.592935"}, {"id": 2145, "name": "Organic Hemp Protection Solution", "issues": ["description contains 'weed'"], "agent": "Simplified Patent Mining Agent", "created": "2025-07-08T20:52:57.820407"}, {"id": 2146, "name": "Hemp-Based Organic Growing Medium", "issues": ["description contains 'weed'"], "agent": "Simplified Patent Mining Agent", "created": "2025-07-08T20:52:58.315055"}, {"id": 2147, "name": "Hemp Protection Agricultural Product", "issues": ["description contains 'weed'"], "agent": "Simplified Patent Mining Agent", "created": "2025-07-08T20:52:59.054773"}, {"id": 2148, "name": "Hemp-Based Protection Growing Medium", "issues": ["description contains 'weed'"], "agent": "Simplified Patent Mining Agent", "created": "2025-07-08T20:52:59.984729"}, {"id": 2014, "name": "THCV Metabolic Gel Caps", "issues": ["name contains 'thc'", "description contains 'thc'"], "agent": "None_quality_improvement", "created": "2025-07-05T02:44:49.811332"}, {"id": 92, "name": "Hemp Flower - Lifter", "issues": ["description contains 'thc'", "description contains 'psychoactive'"], "agent": "None_quality_improvement", "created": "2025-06-07T14:09:55.834724"}], "image_mismatches": [], "agent_issues": []}}