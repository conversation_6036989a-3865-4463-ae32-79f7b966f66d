@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=DM+Sans:wght@400;500;700&family=Space+Grotesk:wght@300;400;500;600;700&family=Source+Sans+3:wght@300;400;500;600;700&display=swap');
@import './styles/typography.css';
@import './styles/animations.css';
@import './styles/theme.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

@font-face {
  font-family: 'SweetLeaf';
  src: url('./assets/fonts/SweetLeaf.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@layer base {
  html {
    font-family: 'Source Sans 3', system-ui, sans-serif;
    font-size: 18px; /* Increased base font size from 16px to 18px */
    @apply text-gray-100; /* Keep text light but remove bg override */
  }
  
  body {
    @apply text-gray-100 min-h-screen;
    margin: 0;
    overflow-x: hidden;
    background-color: #000;
  }
  
  /* Three.js canvas styling */
  canvas {
    display: block;
    width: 100vw;
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    z-index: -1;
  }
  
  /* Use Inter for all headings by default */
  h1, h2, h3, h4, h5, h6 {
    font-family: 'Inter', system-ui, sans-serif;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8), 0 0 20px rgba(0, 0, 0, 0.5);
  }
  
  /* Special hemp font class for brand elements only */
  .font-hemp {
    font-family: 'SweetLeaf', system-ui, sans-serif;
    color: #22c55e !important; /* green-500 */
    font-weight: normal !important;
    letter-spacing: 0.02em;
    font-size: 1.25em; /* Increased size */
  }

  /* Enhanced hemp font with better visibility */
  .font-hemp-enhanced {
    font-family: 'SweetLeaf', system-ui, sans-serif;
    color: #22c55e; /* green-500 */
    font-weight: normal;
    letter-spacing: 0.02em;
    font-size: 1.35em; /* Increased size */
  }

  /* Hemp font for headings with industrial feel */
  .font-hemp-heading {
    font-family: 'SweetLeaf', system-ui, sans-serif;
    color: #22c55e; /* Changed to standard green-500 for consistency */
    font-weight: normal;
    letter-spacing: 0.02em;
    font-size: 1.5em; /* Increased size for headings */
  }

  /* Hemp font for subtle branding */
  .font-hemp-subtle {
    font-family: 'SweetLeaf', system-ui, sans-serif;
    color: #22c55e; /* Changed to standard green-500 for consistency */
    font-weight: normal;
    letter-spacing: 0.02em;
    font-size: 1.15em; /* Slightly increased size */
  }
  
  /* Increase heading sizes */
  h1 {
    @apply text-5xl md:text-6xl lg:text-7xl;
  }
  
  h2 {
    @apply text-4xl md:text-5xl lg:text-6xl;
  }
  
  h3 {
    @apply text-3xl md:text-4xl;
  }
  
  h4 {
    @apply text-2xl md:text-3xl;
  }
  
  h5 {
    @apply text-xl md:text-2xl;
  }
  
  h6 {
    @apply text-lg md:text-xl;
  }
}

/* Custom animations for the animated background */
@keyframes float {
  0%, 100% {
    transform: translateY(0px) translateX(0px);
  }
  25% {
    transform: translateY(-20px) translateX(10px);
  }
  50% {
    transform: translateY(-10px) translateX(-10px);
  }
  75% {
    transform: translateY(-30px) translateX(5px);
  }
}

@keyframes pulse-glow {
  0%, 100% {
    opacity: 0.1;
    transform: scale(1);
  }
  50% {
    opacity: 0.3;
    transform: scale(1.2);
  }
}

@keyframes scan {
  0% {
    transform: translateY(-100px);
    opacity: 0;
  }
  10%, 90% {
    opacity: 1;
  }
  100% {
    transform: translateY(calc(100vh + 100px));
    opacity: 0;
  }
}

@layer utilities {
  .animate-float {
    animation: float 15s ease-in-out infinite;
  }
  
  .animate-pulse-glow {
    animation: pulse-glow 6s ease-in-out infinite;
  }
  
  .animate-scan {
    animation: scan 12s linear infinite;
  }
  
  /* Removed animated background particles to show Three.js background */
  
  /* Increase paragraph and general text sizes */
  p {
    @apply text-base md:text-lg leading-relaxed;
  }
  
  /* Ensure all backgrounds are dark */
  main, section, article, div {
    @apply bg-transparent;
  }
}

/* Add text outline utility classes */
@layer utilities {
  /* For text on dark backgrounds */
  .text-outline-white {
    text-shadow: 
      -1.5px -1.5px 0 #fff,
      1.5px -1.5px 0 #fff,
      -1.5px 1.5px 0 #fff,
      1.5px 1.5px 0 #fff,
      0 0 6px rgba(255, 255, 255, 0.6),
      0 0 10px rgba(255, 255, 255, 0.3);
  }
  
  /* For text on light backgrounds */
  .text-outline-black {
    text-shadow: 
      -1px -1px 0 #000,
      1px -1px 0 #000,
      -1px 1px 0 #000,
      1px 1px 0 #000,
      0 0 5px rgba(0, 0, 0, 0.4);
  }
}

:root {
  /* Updated Professional Color System */
  --primary: 0 0% 0%; /* Black primary #000000 */
  --primary-foreground: 0 0% 100%; /* White text on primary */
  --secondary: 258 90% 66%; /* Purple secondary #8b5cf6 */
  --secondary-foreground: 0 0% 100%; /* White text on secondary */
  --hemp: 142 70% 45%; /* Hemp green #22c55e - preserved */
  --hemp-foreground: 0 0% 100%; /* White text on hemp */
  --background: 221 39% 11%; /* Dark background #111827 */
  --foreground: 0 0% 95%; /* Light text */

  /* Base UI Colors */
  --muted: 217 19% 27%; /* Muted backgrounds */
  --muted-foreground: 215 20% 65%; /* Muted text */
  --popover: 224 71% 4%; /* Popover background */
  --popover-foreground: 0 0% 95%; /* Popover text */
  --card: 224 71% 4%; /* Card background */
  --card-foreground: 0 0% 95%; /* Card text */
  --border: 217 19% 27%; /* Borders */
  --input: 217 19% 27%; /* Input backgrounds */

  /* Semantic Colors */
  --accent: 258 90% 66%; /* Purple accent for consistency */
  --accent-foreground: 0 0% 100%; /* White text on accent */
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 0 0% 98%;
  --ring: 258 90% 66%; /* Purple for focus states */
  --radius: 0.5rem; /* Standard radius */

  /* Hemp-specific variables for component styling */
  --hemp-badge: 142 70% 45%; /* Hemp green for badges */
  --hemp-glow: 142 70% 45%; /* Hemp green for glow effects */
  --category-badge: 258 90% 66%; /* Purple for category badges */
  --interface-bg: 0 0% 0%; /* Black for interface backgrounds */
}
  .dark {
    --background: 221 39% 11%; /* Dark background #111827 */
    --foreground: 0 0% 98%; /* Light text */
    --muted: 217 19% 27%; /* Muted backgrounds */
    --muted-foreground: 215 20% 65%; /* Muted text */
    --popover: 224 71% 4%; /* Popover background */
    --popover-foreground: 0 0% 98%; /* Popover text */
    --card: 224 71% 4%; /* Card background */
    --card-foreground: 0 0% 98%; /* Card text */
    --border: 217 19% 27%; /* Borders */
    --input: 217 19% 27%; /* Input backgrounds */
    --primary: 0 0% 0%; /* Black primary */
    --primary-foreground: 0 0% 100%; /* White text on primary */
    --secondary: 258 90% 66%; /* Purple secondary */
    --secondary-foreground: 0 0% 100%; /* White text on secondary */
    --accent: 258 90% 66%; /* Purple accent */
    --accent-foreground: 0 0% 100%; /* White text on accent */
    --hemp: 142 70% 45%; /* Hemp green - preserved */
    --hemp-foreground: 0 0% 100%; /* White text on hemp */
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --ring: 258 90% 66%; /* Purple for focus states */
    --radius: 0.5rem;
  }

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply antialiased text-gray-100;
    font-family: 'Source Sans 3', system-ui, sans-serif;
    background: transparent !important;
  }
  
  /* Make all major containers transparent to show Three.js background */
  #root, .app-container, main {
    @apply text-gray-100;
    background: transparent !important;
  }
  
  /* Semi-transparent backgrounds for cards and sections */
  .card, section, article {
    @apply text-gray-100;
    background: rgba(17, 24, 39, 0.8) !important; /* bg-gray-900 with 80% opacity */
  }
  
  /* Ensure good contrast for all text */
  p, span, div, li {
    @apply text-gray-100;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
  }
  
  /* Links should be visible */
  a {
    @apply text-green-400 hover:text-green-300;
  }
  
  /* Buttons need proper contrast */
  button {
    @apply text-base md:text-lg font-medium;
  }
  
  /* Input fields */
  input, textarea, select {
    @apply bg-gray-800 text-gray-100 border-gray-700 text-base md:text-lg;
  }
  
  /* Tables */
  table {
    @apply text-gray-200;
  }
  
  th {
    @apply bg-gray-800 text-gray-100 font-semibold;
  }
  
  td {
    @apply bg-gray-900 text-gray-200;
  }
  
  /* Code blocks */
  code, pre {
    @apply bg-gray-800 text-gray-200;
  }
  
  /* Ensure text is readable */
  h1, h2, h3, h4, h5, h6 {
    @apply text-foreground font-semibold;
  }
  
  p, span, div {
    @apply text-foreground;
  }
  
  a {
    @apply text-primary hover:text-primary/80 transition-colors;
  }
  
  /* Form elements */
  input, textarea, select {
    @apply text-foreground bg-background;
  }
  
  /* Buttons text */
  button {
    @apply text-foreground;
  }
  
  /* Fix for components with dark backgrounds */
  .bg-black, .bg-gray-900, .bg-slate-900 {
    @apply text-white;
  }
  
  .bg-black *, .bg-gray-900 *, .bg-slate-900 * {
    @apply text-white;
  }
  
  /* Ensure cards and modals have proper text color */
  .bg-card, [class*="bg-white"], [class*="bg-gray-50"] {
    @apply text-foreground;
  }
  
  /* Fix for primary color text to ensure it's visible */
  .text-primary {
    @apply text-green-600 dark:text-green-400;
  }
  
  /* Ensure muted text is readable */
  .text-muted-foreground {
    @apply text-gray-600 dark:text-gray-400;
  }
  
  /* Fix for overlays and hero sections */
  .bg-black\/50 *, .bg-black\/60 *, .bg-black\/70 * {
    @apply text-white;
  }
  
  /* Ensure forms and inputs are readable */
  .bg-input {
    @apply bg-gray-50 dark:bg-gray-900;
  }
  
  /* Fix card text */
  [data-radix-collection-item] {
    @apply text-foreground;
  }
  
  /* Navbar links on dark background */
  nav.bg-black a {
    @apply text-white hover:text-gray-200;
  }
  
  /* Footer text on dark background */
  footer.bg-gray-900 *, footer.bg-black * {
    @apply text-gray-300;
  }
  
  /* Ensure dropdown menus are readable */
  [role="menu"], [role="listbox"] {
    @apply bg-background text-foreground;
  }
}

@layer utilities {
  .text-stroke-white {
    -webkit-text-stroke: 1px rgba(255, 255, 255, 0.6);
    text-stroke: 1px rgba(255, 255, 255, 0.6);
    text-shadow: 0 0 5px rgba(0, 0, 0, 0.4);
  }
  
  .text-stroke-thin-white {
    -webkit-text-stroke: 0.5px rgba(255, 255, 255, 0.6);
    text-stroke: 0.5px rgba(255, 255, 255, 0.6);
    text-shadow: 0 0 4px rgba(0, 0, 0, 0.4);
  }
  
  .glowing-text {
    color: white !important;
    text-shadow: 
      0 0 5px rgba(255, 255, 255, 1),
      0 0 10px rgba(255, 255, 255, 0.8),
      0 0 15px rgba(255, 255, 255, 0.6),
      0 0 20px rgba(255, 255, 255, 0.4);
    animation: pulse 3s infinite alternate;
    font-weight: 500;
    letter-spacing: 0.01em;
  }
  
  .glow-green-sm {
    text-shadow:
      0 0 5px rgba(74, 222, 128, 0.8),
      0 0 10px rgba(74, 222, 128, 0.5),
      0 0 15px rgba(74, 222, 128, 0.3);
  }

  /* Hemp-specific utility classes for strategic branding */
  .hemp-brand-primary {
    font-family: 'SweetLeaf', system-ui, sans-serif !important;
    color: #22c55e !important; /* green-500 */
    font-weight: normal !important;
    letter-spacing: 0.025em;
    font-size: 1.3em; /* Increased size */
  }

  .hemp-brand-secondary {
    font-family: 'SweetLeaf', system-ui, sans-serif !important;
    color: #22c55e !important; /* Changed to standard green-500 for consistency */
    font-weight: normal !important;
    letter-spacing: 0.02em;
    font-size: 1.25em; /* Increased size */
  }

  .hemp-brand-accent {
    font-family: 'SweetLeaf', system-ui, sans-serif !important;
    color: #22c55e !important; /* Changed to standard green-500 for consistency */
    font-weight: normal !important;
    letter-spacing: 0.02em;
    font-size: 1.2em; /* Increased size */
  }

  /* Modern clean hemp branding - minimal glow */
  .hemp-brand-clean {
    font-family: 'SweetLeaf', system-ui, sans-serif !important;
    color: #22c55e !important; /* green-500 */
    font-weight: normal !important;
    letter-spacing: 0.02em;
    font-size: 1.25em; /* Increased size */
  }

  /* Ultra-modern flat design hemp branding */
  .hemp-brand-flat {
    font-family: 'SweetLeaf', system-ui, sans-serif !important;
    color: #22c55e !important; /* green-500 */
    font-weight: normal !important;
    letter-spacing: 0.02em;
    font-size: 1.25em; /* Increased size */
  }

  /* Modern elevated hemp branding with subtle depth */
  .hemp-brand-elevated {
    font-family: 'SweetLeaf', system-ui, sans-serif !important;
    color: #22c55e !important; /* green-500 */
    font-weight: normal !important;
    letter-spacing: 0.02em;
    font-size: 1.25em; /* Increased size */
  }



  /* Ultra-legible hemp branding for cards and important text */
  .hemp-brand-ultra {
    font-family: 'SweetLeaf', system-ui, sans-serif !important;
    color: #22c55e !important; /* green-500 */
    font-weight: normal !important;
    letter-spacing: 0.025em;
    font-size: 1.4em; /* Increased size for better visibility */
  }

  /* Brighter hemp branding for hover effects */
  .hemp-brand-bright {
    font-family: 'SweetLeaf', system-ui, sans-serif !important;
    color: #22c55e !important; /* Changed to standard green-500 for consistency */
    font-weight: normal !important;
    letter-spacing: 0.025em;
    font-size: 1.45em; /* Increased size for hover effects */
  }
  
  /* Matrix rain effect */
  .bg-matrix-effect {
    background: linear-gradient(0deg, 
      rgba(0,255,0,0.2) 25%, 
      rgba(0,255,0,0.1) 50%, 
      rgba(0,255,0,0.05) 75%, 
      rgba(0,0,0,0) 100%);
    background-size: 100% 400%;
    position: relative;
  }
  
  .bg-matrix-effect::before {
    content: '';
    position: absolute;
    inset: 0;
    background-image: repeating-linear-gradient(0deg,
      rgba(0,255,0,0.3), 
      rgba(0,255,0,0.3) 2px,
      transparent 2px,
      transparent 8px);
    background-size: 100% 8px;
    animation: matrixRain 20s linear infinite;
    opacity: 0.7;
  }
  
  .bg-matrix-effect::after {
    content: '';
    position: absolute;
    inset: 0;
    background-image: 
      repeating-linear-gradient(90deg, 
        rgba(0,20,0,0), 
        rgba(0,20,0,0) 30px, 
        rgba(0,255,0,0.2) 30px, 
        rgba(0,255,0,0.2) 31px),
      repeating-linear-gradient(0deg,
        rgba(0,255,0,0.3), 
        rgba(0,255,0,0.3) 1px,
        transparent 1px,
        transparent 6px);
    background-size: 31px 100%, 100% 6px;
    animation: matrixGlitch 10s infinite;
    opacity: 0.3;
  }
  
  @keyframes matrixRain {
    from {
      background-position: 0 0;
    }
    to {
      background-position: 0 1000px;
    }
  }
  
  @keyframes matrixGlitch {
    0%, 100% {
      opacity: 0.3;
      transform: translateX(0);
    }
    28%, 33% {
      opacity: 0.6;
      transform: translateX(1px);
    }
    62%, 66% {
      opacity: 0.4;
      transform: translateX(-1px);
    }
    82%, 85% {
      opacity: 0.5;
      transform: translateX(1px);
    }
  }
  
  @keyframes pulse {
    from {
      text-shadow: 
        0 0 5px rgba(255, 255, 255, 1),
        0 0 10px rgba(255, 255, 255, 0.8),
        0 0 15px rgba(255, 255, 255, 0.6),
        0 0 20px rgba(255, 255, 255, 0.4);
    }
    to {
      text-shadow: 
        0 0 10px rgba(255, 255, 255, 1),
        0 0 20px rgba(255, 255, 255, 0.9),
        0 0 30px rgba(255, 255, 255, 0.7),
        0 0 40px rgba(255, 255, 255, 0.5);
    }
  }
  
  /* Animation delay utilities */
  .animation-delay-500 {
    animation-delay: 500ms;
  }
  
  .animation-delay-1000 {
    animation-delay: 1000ms;
  }
  
  .animation-delay-1500 {
    animation-delay: 1500ms;
  }
  
  /* Glassmorphism effect */
  .glass {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  /* Pokemon Pokedex Style Effects */
  .holographic {
    background: linear-gradient(135deg, 
      hsl(var(--holographic) / 0.3) 0%, 
      hsl(var(--accent) / 0.3) 25%, 
      hsl(var(--electric) / 0.3) 50%, 
      hsl(var(--holographic) / 0.3) 75%, 
      hsl(var(--accent) / 0.3) 100%);
    background-size: 400% 400%;
    animation: holographic-shift 3s ease infinite;
  }
  
  @keyframes holographic-shift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
  }
  
  .pokedex-border {
    position: relative;
    border: 2px solid hsl(var(--holographic));
    border-radius: 1rem;
    overflow: hidden;
  }
  
  .pokedex-border::before {
    content: '';
    position: absolute;
    inset: -2px;
    background: linear-gradient(45deg, 
      hsl(var(--holographic)), 
      hsl(var(--accent)), 
      hsl(var(--holographic)));
    border-radius: 1rem;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
  }
  
  .pokedex-border:hover::before {
    opacity: 1;
    animation: border-pulse 2s linear infinite;
  }
  
  @keyframes border-pulse {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 1; }
  }
  
  /* Status Badge Colors */
  .status-growing {
    background-color: hsl(var(--status-growing) / 0.2);
    color: hsl(var(--status-growing));
    border: 1px solid hsl(var(--status-growing) / 0.5);
  }
  
  .status-established {
    background-color: hsl(var(--status-established) / 0.2);
    color: hsl(var(--status-established));
    border: 1px solid hsl(var(--status-established) / 0.5);
  }
  
  .status-research {
    background-color: hsl(var(--status-research) / 0.2);
    color: hsl(var(--status-research));
    border: 1px solid hsl(var(--status-research) / 0.5);
  }
  
  .status-speculative {
    background-color: hsl(var(--status-speculative) / 0.2);
    color: hsl(var(--status-speculative));
    border: 1px solid hsl(var(--status-speculative) / 0.5);
  }
  
  /* Glow Effects */
  .glow-teal {
    box-shadow: 
      0 0 20px hsl(var(--holographic) / 0.5),
      0 0 40px hsl(var(--holographic) / 0.3),
      0 0 60px hsl(var(--holographic) / 0.1);
  }
  
  .glow-purple {
    box-shadow: 
      0 0 20px hsl(var(--accent) / 0.5),
      0 0 40px hsl(var(--accent) / 0.3),
      0 0 60px hsl(var(--accent) / 0.1);
  }
  
  .text-glow-teal {
    text-shadow: 
      0 0 10px hsl(var(--holographic) / 0.8),
      0 0 20px hsl(var(--holographic) / 0.6),
      0 0 30px hsl(var(--holographic) / 0.4);
  }
  
  /* 3D Card Effect */
  .card-3d {
    transform-style: preserve-3d;
    transition: transform 0.5s;
  }
  
  .card-3d:hover {
    transform: rotateY(5deg) rotateX(-5deg);
  }
  
  .card-3d-content {
    transform: translateZ(50px);
  }
  
  /* Hover lift effect */
  .hover-lift {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }
  
  .hover-lift:hover {
    transform: translateY(-4px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  }
  
  /* Force dark theme on specific components */
  .bg-white, .bg-gray-50, .bg-gray-100 {
    @apply !bg-gray-900 !text-gray-100;
  }
  
  .bg-gray-200 {
    @apply !bg-gray-800;
  }
  
  .text-black, .text-gray-900, .text-gray-800 {
    @apply !text-gray-100;
  }
  
  .text-gray-700, .text-gray-600 {
    @apply !text-gray-300;
  }
  
  .text-gray-500 {
    @apply !text-gray-400;
  }
  
  /* Ensure cards have dark theme */
  [class*="card"], [class*="Card"] {
    @apply !bg-gray-900 !text-gray-100 !border-gray-700;
  }
  
  /* Navigation specific dark theme */
  nav {
    @apply !bg-black !text-gray-100;
  }
  
  /* Footer dark theme */
  footer {
    @apply !bg-gray-900 !text-gray-300;
  }
  
  /* Modal and dialog dark theme */
  [role="dialog"], [role="alertdialog"] {
    @apply !bg-gray-900 !text-gray-100;
  }
  
  /* Dropdown menus */
  [role="menu"], [role="listbox"] {
    @apply !bg-gray-900 !text-gray-100 !border-gray-700;
  }
  
  /* Tooltips */
  [role="tooltip"] {
    @apply !bg-gray-800 !text-gray-100;
  }
  
  /* Badges and tags */
  .badge, [class*="badge"], [class*="Badge"] {
    @apply !text-sm md:!text-base;
  }
  
  /* Ensure minimum text sizes */
  .text-xs {
    @apply !text-sm;
  }
  
  .text-sm {
    @apply !text-base;
  }
  
  .text-base {
    @apply md:!text-lg;
  }
  
  .text-lg {
    @apply md:!text-xl;
  }
  
  .text-xl {
    @apply md:!text-2xl;
  }
  
  /* Font Family Utilities */
  .font-inter {
    font-family: 'Inter', system-ui, sans-serif !important;
  }
  
  .font-dm-sans {
    font-family: 'DM Sans', system-ui, sans-serif !important;
  }
  
  .font-space-grotesk {
    font-family: 'Space Grotesk', system-ui, sans-serif !important;
  }
  
  .font-source-sans {
    font-family: 'Source Sans 3', system-ui, sans-serif !important;
  }
  
  /* Special classes for different content types */
  .text-stats {
    font-family: 'Space Grotesk', system-ui, sans-serif !important;
    @apply font-medium;
  }
  
  .text-data {
    font-family: 'Space Grotesk', system-ui, sans-serif !important;
  }
  
  /* Button text should use Inter */
  button, .button, [role="button"] {
    font-family: 'Inter', system-ui, sans-serif !important;
    @apply font-medium;
  }
  
  /* Navigation items */
  nav, .nav-item, .menu-item {
    font-family: 'Inter', system-ui, sans-serif !important;
  }
  
  /* Card headers */
  .card-header, .card-title {
    font-family: 'Inter', system-ui, sans-serif !important;
    @apply font-semibold;
  }
}

/* Custom grid for A-Z filter - evenly distributed letters */
@layer utilities {
  .grid-cols-13 {
    grid-template-columns: repeat(13, minmax(0, 1fr));
  }

  .grid-cols-18 {
    grid-template-columns: repeat(18, minmax(0, 1fr));
  }

  .grid-cols-26 {
    grid-template-columns: repeat(26, minmax(0, 1fr));
  }

  /* Responsive adjustments for A-Z filter */
  @media (max-width: 640px) {
    .grid-cols-13 {
      grid-template-columns: repeat(6, minmax(0, 1fr));
    }
    .grid-cols-18 {
      grid-template-columns: repeat(9, minmax(0, 1fr));
    }
    .grid-cols-26 {
      grid-template-columns: repeat(9, minmax(0, 1fr));
    }
  }

  @media (min-width: 641px) and (max-width: 768px) {
    .grid-cols-13 {
      grid-template-columns: repeat(8, minmax(0, 1fr));
    }
    .grid-cols-18 {
      grid-template-columns: repeat(13, minmax(0, 1fr));
    }
    .grid-cols-26 {
      grid-template-columns: repeat(13, minmax(0, 1fr));
    }
  }

  @media (min-width: 769px) and (max-width: 1024px) {
    .grid-cols-13 {
      grid-template-columns: repeat(10, minmax(0, 1fr));
    }
    .grid-cols-18 {
      grid-template-columns: repeat(18, minmax(0, 1fr));
    }
    .grid-cols-26 {
      grid-template-columns: repeat(18, minmax(0, 1fr));
    }
  }
}