import React, { useState, useEffect, useMemo } from "react";
import { useLocation } from "wouter";
import { useAllHempProducts } from "@/hooks/use-product-data";
import { usePlantParts, useIndustries, usePlantTypes } from "@/hooks/use-plant-data";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Search, 
  Filter, 
  Package, 
  Leaf, 
  Factory, 
  TreePine,
  Wheat,
  Flower,
  X,
  Grid3X3,
  List,
  ChevronDown
} from "lucide-react";
import { InteractiveProductCard } from "@/components/product/interactive-product-card";
import { Skeleton } from "@/components/ui/skeleton";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { AdvancedSearchModal } from "@/components/ui/advanced-search-modal";
import { LoadingState } from "@/components/ui/loading-spinner";
import { ErrorState } from "@/components/ui/error-state";

interface Filters {
  search: string;
  plantParts: number[];
  industries: number[];
  stages: string[];
  sortBy: 'name' | 'stage' | 'plantPart' | 'industry';
  viewMode: 'grid' | 'list';
  groupBy: 'none' | 'plantPart' | 'industry' | 'stage';
}

const AllProductsPage = () => {
  const [location, setLocation] = useLocation();
  const { data: products, isLoading: productsLoading, error: productsError } = useAllHempProducts();
  const { data: plantParts, isLoading: partsLoading, error: partsError } = usePlantParts();
  const { data: industries, isLoading: industriesLoading, error: industriesError } = useIndustries();
  const { data: plantTypes } = usePlantTypes();

  // State for user interactions
  const [favorites, setFavorites] = useState<number[]>(() => {
    const saved = localStorage.getItem('hemp-favorites');
    return saved ? JSON.parse(saved) : [];
  });
  const [bookmarks, setBookmarks] = useState<number[]>(() => {
    const saved = localStorage.getItem('hemp-bookmarks');
    return saved ? JSON.parse(saved) : [];
  });
  
  // Parse URL parameters
  const urlParams = new URLSearchParams(window.location.search);
  const tabParam = urlParams.get('tab');
  const searchParam = urlParams.get('search');
  const partsParam = urlParams.get('parts');
  const industriesParam = urlParams.get('industries');
  const stagesParam = urlParams.get('stages');
  const sortParam = urlParams.get('sort');
  const groupParam = urlParams.get('group');
  
  const [filters, setFilters] = useState<Filters>({
    search: searchParam || '',
    plantParts: partsParam ? partsParam.split(',').map(Number) : [],
    industries: industriesParam ? industriesParam.split(',').map(Number) : [],
    stages: stagesParam ? stagesParam.split(',') : [],
    sortBy: sortParam || 'name',
    viewMode: 'grid',
    groupBy: groupParam || 'none'
  });
  
  const [activeTab, setActiveTab] = useState(tabParam || 'all');

  // Handler functions for user interactions
  const handleFavorite = (productId: number) => {
    const newFavorites = favorites.includes(productId)
      ? favorites.filter(id => id !== productId)
      : [...favorites, productId];
    setFavorites(newFavorites);
    localStorage.setItem('hemp-favorites', JSON.stringify(newFavorites));
  };

  const handleBookmark = (productId: number) => {
    const newBookmarks = bookmarks.includes(productId)
      ? bookmarks.filter(id => id !== productId)
      : [...bookmarks, productId];
    setBookmarks(newBookmarks);
    localStorage.setItem('hemp-bookmarks', JSON.stringify(newBookmarks));
  };

  const handleShare = (product: any) => {
    if (navigator.share) {
      navigator.share({
        title: product.name,
        text: product.description,
        url: `${window.location.origin}/product/${product.id}`
      });
    } else {
      // Fallback to clipboard
      navigator.clipboard.writeText(`${window.location.origin}/product/${product.id}`);
      // You could add a toast notification here
    }
  };

  // Update filters when URL search parameter changes
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const searchParam = urlParams.get('search');
    if (searchParam && searchParam !== filters.search) {
      setFilters(prev => ({ ...prev, search: searchParam }));
    }
  }, [location]);
  
  // Get unique stages from products
  const uniqueStages = useMemo(() => {
    if (!products) return [];
    return [...new Set(products.map(p => p.commercialization_stage).filter(Boolean))];
  }, [products]);
  
  // Simple icons for plant parts (MVP approach)
  const getPlantPartIcon = (name: string) => {
    const nameLower = name.toLowerCase();
    if (nameLower.includes('seed')) return <Wheat className="h-5 w-5" />;
    if (nameLower.includes('fiber') || nameLower.includes('bast')) return <TreePine className="h-5 w-5" />;
    if (nameLower.includes('flower')) return <Flower className="h-5 w-5" />;
    if (nameLower.includes('hurd') || nameLower.includes('shiv')) return <Package className="h-5 w-5" />;
    return <Leaf className="h-5 w-5" />;
  };
  
  // Simple icons for industries
  const getIndustryIcon = (name: string) => {
    const nameLower = name.toLowerCase();
    if (nameLower.includes('industrial') || nameLower.includes('manufacturing')) return <Factory className="h-5 w-5" />;
    return <Package className="h-5 w-5" />;
  };
  
  // Filter and sort products
  const filteredProducts = useMemo(() => {
    if (!products) return [];
    
    let result = [...products];
    
    // Search filter
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      result = result.filter(product => 
        product.name.toLowerCase().includes(searchLower) ||
        product.description?.toLowerCase().includes(searchLower) ||
        product.benefits_advantages?.some(b => b.toLowerCase().includes(searchLower)) ||
        product.keywords?.some(k => k.toLowerCase().includes(searchLower))
      );
    }
    
    // Plant part filter
    if (filters.plantParts.length > 0) {
      result = result.filter(product => 
        filters.plantParts.includes(product.plant_part_id)
      );
    }
    
    // Industry filter
    if (filters.industries.length > 0) {
      result = result.filter(product => {
        // Need to check if product's sub-category belongs to selected industries
        // This is a simplified check - you may need to adjust based on your data structure
        return product.industry_sub_category_id && filters.industries.length > 0;
      });
    }
    
    // Stage filter
    if (filters.stages.length > 0) {
      result = result.filter(product => 
        filters.stages.includes(product.commercialization_stage || '')
      );
    }
    
    // Sorting
    result.sort((a, b) => {
      switch (filters.sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'name-desc':
          return b.name.localeCompare(a.name);
        case 'stage':
          return (a.commercialization_stage || '').localeCompare(b.commercialization_stage || '');
        case 'plantPart':
          return a.plant_part_id - b.plant_part_id;
        case 'industry':
          return (a.industry_sub_category_id || 0) - (b.industry_sub_category_id || 0);
        case 'sustainability':
          // Sort by sustainability score if available in data
          return 0; // Placeholder - implement when sustainability data is available
        case 'newest':
          // Sort by created date if available
          return 0; // Placeholder - implement when created date is available
        case 'relevance':
        default:
          // For relevance, prioritize search matches
          if (filters.search) {
            const searchLower = filters.search.toLowerCase();
            const aScore = (a.name.toLowerCase().includes(searchLower) ? 10 : 0) +
                          (a.description?.toLowerCase().includes(searchLower) ? 5 : 0);
            const bScore = (b.name.toLowerCase().includes(searchLower) ? 10 : 0) +
                          (b.description?.toLowerCase().includes(searchLower) ? 5 : 0);
            return bScore - aScore;
          }
          return 0;
      }
    });
    
    return result;
  }, [products, filters]);
  
  // Group products if needed
  const groupedProducts = useMemo(() => {
    if (filters.groupBy === 'none') return { 'All Products': filteredProducts };
    
    const groups: Record<string, typeof filteredProducts> = {};
    
    filteredProducts.forEach(product => {
      let groupKey = 'Other';
      
      if (filters.groupBy === 'plantPart') {
        const part = plantParts?.find(p => p.id === product.plant_part_id);
        groupKey = part?.name || 'Unknown Part';
      } else if (filters.groupBy === 'stage') {
        groupKey = product.commercialization_stage || 'Unknown Stage';
      }
      
      if (!groups[groupKey]) groups[groupKey] = [];
      groups[groupKey].push(product);
    });
    
    return groups;
  }, [filteredProducts, filters.groupBy, plantParts]);
  
  // Handle category selection
  const handlePlantPartSelect = (partId: number) => {
    setFilters(prev => ({
      ...prev,
      plantParts: prev.plantParts.includes(partId)
        ? prev.plantParts.filter(id => id !== partId)
        : [...prev.plantParts, partId]
    }));
  };
  
  const handleIndustrySelect = (industryId: number) => {
    setFilters(prev => ({
      ...prev,
      industries: prev.industries.includes(industryId)
        ? prev.industries.filter(id => id !== industryId)
        : [...prev.industries, industryId]
    }));
  };
  
  const handleStageSelect = (stage: string) => {
    setFilters(prev => ({
      ...prev,
      stages: prev.stages.includes(stage)
        ? prev.stages.filter(s => s !== stage)
        : [...prev.stages, stage]
    }));
  };
  
  const clearFilters = () => {
    setFilters({
      search: '',
      plantParts: [],
      industries: [],
      stages: [],
      sortBy: 'name',
      viewMode: 'grid',
      groupBy: 'none'
    });
  };
  
  const hasActiveFilters = filters.search || filters.plantParts.length > 0 || 
                          filters.industries.length > 0 || filters.stages.length > 0;
  
  const isLoading = productsLoading || partsLoading || industriesLoading;
  const hasError = productsError || partsError || industriesError;
  
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-950 to-gray-900">
      {/* Header */}
      <div className="bg-gradient-to-r from-hemp-800 via-hemp-600 to-hemp-500 p-1">
        <div className="bg-black p-6">
          <div className="max-w-7xl mx-auto">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h1 className="text-3xl sm:text-4xl font-heading font-bold text-white">
                  All Products
                </h1>
                <p className="text-gray-300 mt-2">
                  Discover {products?.length || 0} innovative hemp products across all industries
                </p>
              </div>
              
              {/* View Controls */}
              <div className="flex items-center gap-4">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" className="bg-gray-800 border-gray-700">
                      Sort: {filters.sortBy}
                      <ChevronDown className="ml-2 h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="bg-gray-800 border-gray-700">
                    <DropdownMenuItem onClick={() => setFilters(prev => ({ ...prev, sortBy: 'name' }))}>
                      Name
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => setFilters(prev => ({ ...prev, sortBy: 'stage' }))}>
                      Development Stage
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => setFilters(prev => ({ ...prev, sortBy: 'plantPart' }))}>
                      Plant Part
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
                
                <div className="flex gap-2">
                  <Button
                    variant={filters.viewMode === 'grid' ? 'default' : 'outline'}
                    size="icon"
                    onClick={() => setFilters(prev => ({ ...prev, viewMode: 'grid' }))}
                    className="bg-gray-800 border-gray-700"
                  >
                    <Grid3X3 className="h-4 w-4" />
                  </Button>
                  <Button
                    variant={filters.viewMode === 'list' ? 'default' : 'outline'}
                    size="icon"
                    onClick={() => setFilters(prev => ({ ...prev, viewMode: 'list' }))}
                    className="bg-gray-800 border-gray-700"
                  >
                    <List className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
            
            {/* Search Bar with Advanced Search */}
            <div className="flex gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <Input
                  type="text"
                  placeholder="Search products by name, benefits, or keywords..."
                  value={filters.search}
                  onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                  className="w-full pl-10 pr-4 py-3 bg-gray-900/50 border-gray-700 text-white placeholder-gray-400 focus:border-secondary-500"
                />
              </div>
              <AdvancedSearchModal 
                onSearch={(advancedFilters) => {
                  setFilters({
                    ...filters,
                    ...advancedFilters
                  });
                }}
                currentFilters={filters}
              />
            </div>
          </div>
        </div>
      </div>
      
      {/* Category Tabs */}
      <div className="bg-gray-800 border-b border-gray-700">
        <div className="max-w-7xl mx-auto px-4">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="bg-transparent border-0 p-0 h-auto">
              <TabsTrigger value="all" className="data-[state=active]:bg-gray-700">
                All Products
              </TabsTrigger>
              <TabsTrigger value="plant-parts" className="data-[state=active]:bg-gray-700">
                By Plant Part
              </TabsTrigger>
              <TabsTrigger value="industries" className="data-[state=active]:bg-gray-700">
                By Industry
              </TabsTrigger>
              <TabsTrigger value="stages" className="data-[state=active]:bg-gray-700">
                By Stage
              </TabsTrigger>
            </TabsList>
            
            {/* Category Selectors */}
            <div className="py-4">
              <TabsContent value="all" className="mt-0">
                <p className="text-gray-400">
                  Showing all products. Use filters or browse by category.
                </p>
              </TabsContent>
              
              <TabsContent value="plant-parts" className="mt-0">
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-3">
                  {plantParts?.map(part => {
                    const count = products?.filter(p => p.plant_part_id === part.id).length || 0;
                    const isSelected = filters.plantParts.includes(part.id);
                    
                    return (
                      <button
                        key={part.id}
                        onClick={() => handlePlantPartSelect(part.id)}
                        className={`p-4 rounded-lg border-2 transition-all ${
                          isSelected 
                            ? 'border-green-500 bg-green-500/20' 
                            : 'border-gray-700 bg-gray-800 hover:border-gray-600'
                        }`}
                      >
                        <div className="flex flex-col items-center gap-2">
                          <div className={isSelected ? 'text-green-400' : 'text-gray-400'}>
                            {getPlantPartIcon(part.name)}
                          </div>
                          <span className="text-sm font-medium text-white">{part.name}</span>
                          <span className="text-xs text-gray-400">{count} products</span>
                        </div>
                      </button>
                    );
                  })}
                </div>
              </TabsContent>
              
              <TabsContent value="industries" className="mt-0">
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-3">
                  {industries?.map(industry => {
                    const count = products?.length || 0; // Simplified count
                    const isSelected = filters.industries.includes(industry.id);
                    
                    return (
                      <button
                        key={industry.id}
                        onClick={() => handleIndustrySelect(industry.id)}
                        className={`p-4 rounded-lg border-2 transition-all ${
                          isSelected 
                            ? 'border-green-500 bg-green-500/20' 
                            : 'border-gray-700 bg-gray-800 hover:border-gray-600'
                        }`}
                      >
                        <div className="flex flex-col items-center gap-2">
                          <div className={isSelected ? 'text-green-400' : 'text-gray-400'}>
                            {getIndustryIcon(industry.name)}
                          </div>
                          <span className="text-sm font-medium text-white">{industry.name}</span>
                          <span className="text-xs text-gray-400">{count} products</span>
                        </div>
                      </button>
                    );
                  })}
                </div>
              </TabsContent>
              
              <TabsContent value="stages" className="mt-0">
                <div className="flex flex-wrap gap-3">
                  {uniqueStages.map(stage => {
                    const count = products?.filter(p => p.commercialization_stage === stage).length || 0;
                    const isSelected = filters.stages.includes(stage);
                    
                    return (
                      <button
                        key={stage}
                        onClick={() => handleStageSelect(stage)}
                        className={`px-6 py-3 rounded-lg border-2 transition-all ${
                          isSelected 
                            ? 'border-green-500 bg-green-500/20' 
                            : 'border-gray-700 bg-gray-800 hover:border-gray-600'
                        }`}
                      >
                        <span className="font-medium text-white">{stage}</span>
                        <span className="ml-2 text-sm text-gray-400">({count})</span>
                      </button>
                    );
                  })}
                </div>
              </TabsContent>
            </div>
          </Tabs>
        </div>
      </div>
      
      {/* Active Filters Bar */}
      {hasActiveFilters && (
        <div className="bg-gray-800/50 border-b border-gray-700">
          <div className="max-w-7xl mx-auto px-4 py-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2 flex-wrap">
                <span className="text-sm text-gray-400">Active filters:</span>
                
                {filters.search && (
                  <Badge variant="hemp" className="bg-hemp-500/20 text-hemp-400">
                    Search: "{filters.search}"
                    <X
                      className="ml-1 h-3 w-3 cursor-pointer"
                      onClick={() => setFilters(prev => ({ ...prev, search: '' }))}
                    />
                  </Badge>
                )}
                
                {filters.plantParts.map(partId => {
                  const part = plantParts?.find(p => p.id === partId);
                  return part ? (
                    <Badge key={partId} variant="plantPart">
                      {part.name}
                      <X
                        className="ml-1 h-3 w-3 cursor-pointer"
                        onClick={() => handlePlantPartSelect(partId)}
                      />
                    </Badge>
                  ) : null;
                })}
                
                {filters.industries.map(industryId => {
                  const industry = industries?.find(i => i.id === industryId);
                  return industry ? (
                    <Badge key={industryId} variant="industry">
                      {industry.name}
                      <X
                        className="ml-1 h-3 w-3 cursor-pointer"
                        onClick={() => handleIndustrySelect(industryId)}
                      />
                    </Badge>
                  ) : null;
                })}
                
                {filters.stages.map(stage => (
                  <Badge key={stage} variant="secondary" className="bg-yellow-500/20 text-yellow-400">
                    {stage}
                    <X 
                      className="ml-1 h-3 w-3 cursor-pointer" 
                      onClick={() => handleStageSelect(stage)}
                    />
                  </Badge>
                ))}
              </div>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={clearFilters}
                className="text-gray-400 hover:text-white"
              >
                Clear all
              </Button>
            </div>
          </div>
        </div>
      )}
      
      {/* Results */}
      <div className="max-w-7xl mx-auto px-4 py-8">
        <div className="mb-4 flex items-center justify-between">
          <p className="text-gray-400">
            Showing {filteredProducts.length} of {products?.length || 0} products
          </p>
          
          {filters.groupBy !== 'none' && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setFilters(prev => ({ ...prev, groupBy: 'none' }))}
              className="text-gray-400"
            >
              Remove grouping
            </Button>
          )}
        </div>
        
        {hasError ? (
          <ErrorState 
            title="Unable to load products"
            message="There was an error loading the product data. Please check your connection and try again."
            onRetry={() => window.location.reload()}
          />
        ) : isLoading ? (
          <LoadingState message="Loading hemp products..." />
        ) : (
          <div>
            {Object.entries(groupedProducts).map(([groupName, groupProducts]) => (
              <div key={groupName} className="mb-8">
                {filters.groupBy !== 'none' && (
                  <h3 className="text-xl font-semibold text-white mb-4">
                    {groupName} ({groupProducts.length})
                  </h3>
                )}
                
                <div className={
                  filters.viewMode === 'grid' 
                    ? "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
                    : "space-y-4"
                }>
                  {groupProducts.map(product => {
                    // Create lookup objects for the InteractiveProductCard
                    const industryNames: Record<number, string> = {};
                    const subIndustryNames: Record<number, string> = {};
                    const plantPartNames: Record<number, string> = {};

                    industries?.forEach(industry => {
                      industryNames[industry.id] = industry.name;
                    });

                    plantParts?.forEach(part => {
                      plantPartNames[part.id] = part.name;
                    });

                    return (
                      <InteractiveProductCard
                        key={product.id}
                        product={product}
                        industryNames={industryNames}
                        subIndustryNames={subIndustryNames}
                        plantPartNames={plantPartNames}
                        variant={filters.viewMode === 'list' ? 'compact' : 'default'}
                        showActions={true}
                        showStats={false}
                        onFavorite={handleFavorite}
                        onShare={handleShare}
                        onBookmark={handleBookmark}
                        isFavorited={favorites.includes(product.id)}
                        isBookmarked={bookmarks.includes(product.id)}
                      />
                    );
                  })}
                </div>
              </div>
            ))}
            
            {filteredProducts.length === 0 && (
              <div className="text-center py-12">
                <Package className="h-16 w-16 text-gray-600 mx-auto mb-4" />
                <h3 className="text-xl font-medium text-white mb-2">No products found</h3>
                <p className="text-gray-400 mb-4">
                  Try adjusting your filters or search terms
                </p>
                <Button onClick={clearFilters} variant="outline">
                  Clear filters
                </Button>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default AllProductsPage;