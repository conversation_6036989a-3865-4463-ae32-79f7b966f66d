import { useAllHempProducts } from "@/hooks/use-product-data";
import { ProductGridSkeleton } from "@/components/ui/skeleton";
import { useState, useMemo } from "react";
import EnhancedProductCard from "@/components/product/enhanced-product-card";
import { Search, SlidersHorizontal, ChevronLeft, ChevronRight } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

const SimplifiedProductsPage = () => {
  const { data: products, isLoading } = useAllHempProducts();
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedStage, setSelectedStage] = useState<string | null>(null);
  const [selectedLetter, setSelectedLetter] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(12);

  // Generate alphabet array
  const alphabet = Array.from({ length: 26 }, (_, i) => String.fromCharCode(65 + i));

  // Quick filters - only show 3-4 most important
  const quickFilters = ["Commercial", "Development", "Research"];

  // Filter and paginate products
  const { filteredProducts, totalPages, paginatedProducts } = useMemo(() => {
    if (!products) return { filteredProducts: [], totalPages: 0, paginatedProducts: [] };

    let filtered = products.filter(product => {
      const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           product.description?.toLowerCase().includes(searchTerm.toLowerCase());
      const productStage = product.commercialization_stage || product.commercializationStage;
      const matchesStage = !selectedStage || productStage === selectedStage;
      const matchesLetter = !selectedLetter || product.name.charAt(0).toUpperCase() === selectedLetter;
      return matchesSearch && matchesStage && matchesLetter;
    });

    // Sort alphabetically
    filtered.sort((a, b) => a.name.localeCompare(b.name));

    const totalPages = Math.ceil(filtered.length / itemsPerPage);
    const startIndex = (currentPage - 1) * itemsPerPage;
    const paginatedProducts = filtered.slice(startIndex, startIndex + itemsPerPage);

    return { filteredProducts: filtered, totalPages, paginatedProducts };
  }, [products, searchTerm, selectedStage, selectedLetter, currentPage, itemsPerPage]);

  // Reset page when filters change
  const handleFilterChange = () => {
    setCurrentPage(1);
  };

  // Pagination handlers
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  return (
    <div className="min-h-screen bg-neutral-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-white mb-2">Hemp Products</h1>
          <p className="text-neutral-400">
            {filteredProducts.length} products found
          </p>
        </div>

        {/* Search Bar */}
        <div className="relative mb-6">
          <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-neutral-400" />
          <input
            type="text"
            placeholder="Search products..."
            value={searchTerm}
            onChange={(e) => {
              setSearchTerm(e.target.value);
              handleFilterChange();
            }}
            className="w-full pl-12 pr-4 py-3 bg-neutral-800 border border-neutral-700 rounded-lg text-white placeholder-neutral-400 focus:outline-none focus:border-hemp-400 focus:ring-1 focus:ring-hemp-400"
          />
        </div>

        {/* Quick Filters */}
        <div className="flex items-center gap-4 mb-6">
          <div className="flex gap-2">
            <Badge
              variant={selectedStage === null ? "default" : "outline"}
              className={`cursor-pointer ${selectedStage === null ? 'bg-hemp-400 text-white' : 'text-neutral-400 border-neutral-600'}`}
              onClick={() => {
                setSelectedStage(null);
                handleFilterChange();
              }}
            >
              All Stages
            </Badge>
            {quickFilters.map(stage => (
              <Badge
                key={stage}
                variant={selectedStage === stage ? "default" : "outline"}
                className={`cursor-pointer ${selectedStage === stage ? 'bg-hemp-400 text-white' : 'text-neutral-400 border-neutral-600'}`}
                onClick={() => {
                  setSelectedStage(stage);
                  handleFilterChange();
                }}
              >
                {stage}
              </Badge>
            ))}
          </div>
          
          {/* Advanced Filters Dropdown */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="ml-auto">
                <SlidersHorizontal className="h-4 w-4 mr-2" />
                More Filters
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuItem>Industry Categories</DropdownMenuItem>
              <DropdownMenuItem>Plant Parts</DropdownMenuItem>
              <DropdownMenuItem>Companies</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        {/* A-Z Filter */}
        <div className="mb-6">
          <div className="flex flex-wrap gap-1">
            <Button
              variant={selectedLetter === null ? "default" : "ghost"}
              size="sm"
              className={`h-8 w-8 p-0 ${selectedLetter === null ? 'bg-hemp-400 text-white' : 'text-neutral-400'}`}
              onClick={() => {
                setSelectedLetter(null);
                handleFilterChange();
              }}
            >
              All
            </Button>
            {alphabet.map(letter => (
              <Button
                key={letter}
                variant={selectedLetter === letter ? "default" : "ghost"}
                size="sm"
                className={`h-8 w-8 p-0 ${selectedLetter === letter ? 'bg-hemp-400 text-white' : 'text-neutral-400'}`}
                onClick={() => {
                  setSelectedLetter(letter);
                  handleFilterChange();
                }}
              >
                {letter}
              </Button>
            ))}
          </div>
        </div>

        {/* Results Count and Items Per Page */}
        <div className="flex justify-between items-center mb-6">
          <p className="text-sm text-neutral-400">
            Showing {((currentPage - 1) * itemsPerPage) + 1}-{Math.min(currentPage * itemsPerPage, filteredProducts.length)} of {filteredProducts.length} results
          </p>
          <Select
            value={itemsPerPage.toString()}
            onValueChange={(value) => {
              setItemsPerPage(parseInt(value));
              setCurrentPage(1);
            }}
          >
            <SelectTrigger className="w-24">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="6">6</SelectItem>
              <SelectItem value="12">12</SelectItem>
              <SelectItem value="24">24</SelectItem>
              <SelectItem value="48">48</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Product Grid */}
        {isLoading ? (
          <ProductGridSkeleton count={itemsPerPage} />
        ) : (
          <>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
              {paginatedProducts.map((product) => (
                <EnhancedProductCard key={product.id} product={product} />
              ))}
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex justify-center items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                  className="text-neutral-400"
                >
                  <ChevronLeft className="h-4 w-4" />
                  Previous
                </Button>
                
                <div className="flex gap-1">
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    let pageNum;
                    if (totalPages <= 5) {
                      pageNum = i + 1;
                    } else if (currentPage <= 3) {
                      pageNum = i + 1;
                    } else if (currentPage >= totalPages - 2) {
                      pageNum = totalPages - 4 + i;
                    } else {
                      pageNum = currentPage - 2 + i;
                    }
                    
                    return (
                      <Button
                        key={i}
                        variant={currentPage === pageNum ? "default" : "outline"}
                        size="sm"
                        onClick={() => handlePageChange(pageNum)}
                        className={currentPage === pageNum ? 'bg-hemp-400 text-white' : 'text-neutral-400'}
                      >
                        {pageNum}
                      </Button>
                    );
                  })}
                </div>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === totalPages}
                  className="text-neutral-400"
                >
                  Next
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default SimplifiedProductsPage;