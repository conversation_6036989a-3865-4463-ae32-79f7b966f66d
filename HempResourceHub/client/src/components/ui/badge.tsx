import * as React from "react";
import { cva, type VariantProps } from "class-variance-authority";

import { cn } from "@/lib/utils";

const badgeVariants = cva(
  "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      variant: {
        default:
          "border-transparent bg-gray-800 text-gray-100 hover:bg-gray-700",
        secondary:
          "border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",
        destructive:
          "border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",
        outline: "text-foreground border-gray-700",
        hemp: "border-hemp-500/30 bg-hemp-500/20 text-hemp-400 hover:bg-hemp-500/30",
        category: "border-secondary-500/30 bg-secondary-500/20 text-secondary-400 hover:bg-secondary-500/30",
        plantPart: "border-hemp-500/30 bg-hemp-500/20 text-hemp-400 hover:bg-hemp-500/30",
        industry: "border-secondary-500/30 bg-secondary-500/20 text-secondary-400 hover:bg-secondary-500/30",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  },
);

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {}

function Badge({ className, variant, ...props }: BadgeProps) {
  return (
    <div
      className={cn(badgeVariants({ variant }), className)}
      {...props}
      data-oid="y-q6e2r"
    />
  );
}

export { Badge, badgeVariants };
