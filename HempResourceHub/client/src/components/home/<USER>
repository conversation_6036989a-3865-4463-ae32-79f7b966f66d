import { useState, useMemo, memo, useCallback } from "react";
import { useAllHempProducts } from "@/hooks/use-product-data";
import { usePlantParts, useIndustries } from "@/hooks/use-plant-data";
import { usePerformanceMonitor } from "@/hooks/use-performance";
import { InteractiveProductCard } from "@/components/product/interactive-product-card";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  ArrowRight,
  Star,
  TrendingUp,
  Sparkles,
  ChevronLeft,
  ChevronRight
} from "lucide-react";
import { useLocation } from "wouter";
import { Skeleton } from "@/components/ui/skeleton";

const FeaturedProducts = () => {
  usePerformanceMonitor('FeaturedProducts');

  const [, setLocation] = useLocation();
  const { data: products, isLoading: productsLoading } = useAllHempProducts();
  const { data: plantParts } = usePlantParts();
  const { data: industries } = useIndustries();
  
  // State for user interactions
  const [favorites, setFavorites] = useState<number[]>(() => {
    const saved = localStorage.getItem('hemp-favorites');
    return saved ? JSON.parse(saved) : [];
  });
  const [bookmarks, setBookmarks] = useState<number[]>(() => {
    const saved = localStorage.getItem('hemp-bookmarks');
    return saved ? JSON.parse(saved) : [];
  });
  const [currentSlide, setCurrentSlide] = useState(0);
  const [touchStart, setTouchStart] = useState<number | null>(null);
  const [touchEnd, setTouchEnd] = useState<number | null>(null);

  // Create lookup objects for names
  const industryNames = useMemo(() => {
    if (!industries) return {};
    return industries.reduce((acc, industry) => {
      acc[industry.id] = industry.name;
      return acc;
    }, {} as Record<number, string>);
  }, [industries]);

  const plantPartNames = useMemo(() => {
    if (!plantParts) return {};
    return plantParts.reduce((acc, part) => {
      acc[part.id] = part.name;
      return acc;
    }, {} as Record<number, string>);
  }, [plantParts]);

  // Select featured products (trending, popular, or recently added)
  const featuredProducts = useMemo(() => {
    if (!products || products.length === 0) return [];

    // Filter for products with good content
    const qualityProducts = products.filter(product =>
      product.name &&
      product.description &&
      product.description.length > 30 &&
      product.plant_part_id &&
      product.industry_id
    );

    // Sort by various criteria to get diverse featured products
    const sortedProducts = qualityProducts.sort((a, b) => {
      // Prioritize products with established commercialization stage
      if (a.commercialization_stage === 'Established' && b.commercialization_stage !== 'Established') return -1;
      if (b.commercialization_stage === 'Established' && a.commercialization_stage !== 'Established') return 1;

      // Prioritize products with benefits/advantages
      const aBenefits = Array.isArray(a.benefits) ? a.benefits.length : 0;
      const bBenefits = Array.isArray(b.benefits) ? b.benefits.length : 0;
      if (aBenefits !== bBenefits) return bBenefits - aBenefits;

      // Then by name length (longer names often indicate more specific/interesting products)
      return b.name.length - a.name.length;
    });

    return sortedProducts.slice(0, 9); // Get top 9 featured products for better carousel
  }, [products]);

  const handleFavorite = useCallback((productId: number) => {
    const newFavorites = favorites.includes(productId)
      ? favorites.filter(id => id !== productId)
      : [...favorites, productId];
    setFavorites(newFavorites);
    localStorage.setItem('hemp-favorites', JSON.stringify(newFavorites));
  }, [favorites]);

  const handleBookmark = useCallback((productId: number) => {
    const newBookmarks = bookmarks.includes(productId)
      ? bookmarks.filter(id => id !== productId)
      : [...bookmarks, productId];
    setBookmarks(newBookmarks);
    localStorage.setItem('hemp-bookmarks', JSON.stringify(newBookmarks));
  }, [bookmarks]);

  const handleShare = useCallback((product: any) => {
    if (navigator.share) {
      navigator.share({
        title: product.name,
        text: product.description,
        url: `${window.location.origin}/product/${product.id}`
      });
    } else {
      navigator.clipboard.writeText(`${window.location.origin}/product/${product.id}`);
    }
  }, []);

  const nextSlide = useCallback(() => {
    setCurrentSlide((prev) => (prev + 1) % Math.max(1, featuredProducts.length - 2));
  }, [featuredProducts.length]);

  const prevSlide = useCallback(() => {
    setCurrentSlide((prev) => (prev - 1 + Math.max(1, featuredProducts.length - 2)) % Math.max(1, featuredProducts.length - 2));
  }, [featuredProducts.length]);

  // Touch handlers for mobile swipe
  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0].clientX);
  }, []);

  const handleTouchMove = useCallback((e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX);
  }, []);

  const handleTouchEnd = useCallback(() => {
    if (!touchStart || !touchEnd) return;

    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > 50;
    const isRightSwipe = distance < -50;

    if (isLeftSwipe && featuredProducts.length > 3) {
      nextSlide();
    }
    if (isRightSwipe && featuredProducts.length > 3) {
      prevSlide();
    }
  }, [touchStart, touchEnd, featuredProducts.length, nextSlide, prevSlide]);

  if (productsLoading) {
    return (
      <section className="py-16 bg-gray-950/30">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <Skeleton className="h-8 w-64 mx-auto mb-4" />
            <Skeleton className="h-4 w-96 mx-auto" />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[1, 2, 3].map((i) => (
              <Skeleton key={i} className="h-96 rounded-xl" />
            ))}
          </div>
        </div>
      </section>
    );
  }

  if (!featuredProducts || featuredProducts.length === 0) {
    return (
      <section className="py-16 bg-gray-950/30">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-green-400/10 border border-green-400/20 mb-4">
            <Star className="w-4 h-4 text-green-400" />
            <span className="text-sm font-medium text-green-400">Featured Applications</span>
          </div>

          <h2 className="text-3xl sm:text-4xl font-bold text-white mb-4">
            Hemp Applications
            <span className="bg-gradient-to-r from-green-400 to-emerald-400 bg-clip-text text-transparent"> Coming Soon</span>
          </h2>

          <p className="text-lg text-gray-300 max-w-2xl mx-auto mb-8">
            We're curating the most innovative hemp applications for you. Check back soon!
          </p>

          <Button
            size="lg"
            onClick={() => setLocation('/hemp-dex')}
            className="bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-white"
          >
            <Sparkles className="w-4 h-4 mr-2" />
            Explore Hemp Database
            <ArrowRight className="w-4 h-4 ml-2" />
          </Button>
        </div>
      </section>
    );
  }

  return (
    <section className="py-16 bg-gray-950/30 relative overflow-hidden">
      {/* Background decoration */}
      <div className="absolute inset-0 bg-gradient-to-r from-green-900/5 via-transparent to-emerald-900/5" />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        {/* Section Header */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-hemp-400/10 border border-hemp-400/20 mb-4">
            <Star className="w-4 h-4 text-hemp-400" />
            <span className="text-sm font-medium text-hemp-400">Featured Applications</span>
          </div>
          
          <h2 className="text-3xl sm:text-4xl font-bold text-white mb-4">
            Trending Hemp 
            <span className="bg-gradient-to-r from-hemp-400 to-hemp-300 bg-clip-text text-transparent"> Innovations</span>
          </h2>
          
          <p className="text-lg text-gray-300 max-w-3xl mx-auto">
            Discover the most promising hemp applications that are revolutionizing industries 
            and creating sustainable solutions for the future.
          </p>
        </div>

        {/* Featured Products Grid */}
        <div className="relative">
          <div
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8 touch-pan-y"
            onTouchStart={handleTouchStart}
            onTouchMove={handleTouchMove}
            onTouchEnd={handleTouchEnd}
          >
            {featuredProducts.slice(currentSlide, currentSlide + 3).map((product, index) => (
              <div key={product.id} className="relative">
                {index === 0 && (
                  <div className="absolute -top-3 -right-3 z-10">
                    <Badge className="bg-gradient-to-r from-yellow-400 to-orange-400 text-black font-semibold px-3 py-1">
                      <TrendingUp className="w-3 h-3 mr-1" />
                      Trending
                    </Badge>
                  </div>
                )}
                
                <InteractiveProductCard
                  product={product}
                  industryNames={industryNames}
                  subIndustryNames={{}} // We'll add this if needed
                  plantPartNames={plantPartNames}
                  variant={index === 0 ? 'featured' : 'default'}
                  showActions={true}
                  showStats={false}
                  onFavorite={handleFavorite}
                  onShare={handleShare}
                  onBookmark={handleBookmark}
                  isFavorited={favorites.includes(product.id)}
                  isBookmarked={bookmarks.includes(product.id)}
                />
              </div>
            ))}
          </div>

          {/* Navigation Controls - Enhanced for mobile */}
          {featuredProducts.length > 3 && (
            <div className="flex items-center justify-center gap-4">
              <Button
                variant="outline"
                size="sm"
                onClick={prevSlide}
                className="border-gray-700 text-gray-300 hover:bg-gray-800 min-h-[44px] min-w-[44px] touch-manipulation"
                aria-label="Previous products"
              >
                <ChevronLeft className="w-4 h-4" />
              </Button>

              <div className="flex gap-3">
                {Array.from({ length: Math.max(1, featuredProducts.length - 2) }).map((_, index) => (
                  <button
                    key={index}
                    onClick={() => setCurrentSlide(index)}
                    className={`w-3 h-3 md:w-2 md:h-2 rounded-full transition-colors touch-manipulation min-h-[44px] min-w-[44px] md:min-h-auto md:min-w-auto flex items-center justify-center ${
                      index === currentSlide ? 'bg-green-400' : 'bg-gray-600'
                    }`}
                    aria-label={`Go to slide ${index + 1}`}
                  >
                    <span className={`w-3 h-3 md:w-2 md:h-2 rounded-full ${
                      index === currentSlide ? 'bg-green-400' : 'bg-gray-600'
                    }`} />
                  </button>
                ))}
              </div>

              <Button
                variant="outline"
                size="sm"
                onClick={nextSlide}
                className="border-gray-700 text-gray-300 hover:bg-gray-800 min-h-[44px] min-w-[44px] touch-manipulation"
                aria-label="Next products"
              >
                <ChevronRight className="w-4 h-4" />
              </Button>
            </div>
          )}
        </div>

        {/* Call to Action */}
        <div className="text-center mt-12">
          <Button
            size="lg"
            onClick={() => setLocation('/products')}
            className="bg-gradient-to-r from-hemp-500 to-hemp-400 hover:from-hemp-600 hover:to-hemp-500 text-white group"
          >
            <Sparkles className="w-4 h-4 mr-2" />
            Explore All {products?.length || 0}+ Hemp Applications
            <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
          </Button>
        </div>
      </div>
    </section>
  );
};

export default memo(FeaturedProducts);
