import { useEffect, useState } from "react";
import { <PERSON> } from "wouter";
import { motion, AnimatePresence } from "framer-motion";
import { 
  Leaf, 
  Building2, 
  TrendingUp, 
  Search, 
  Package, 
  Users,
  BarChart3,
  Sparkles,
  FlaskConical,
  Globe,
  ArrowRight,
  CircleIcon,
  Factory,
  Sprout,
  TreePine
} from "lucide-react";
import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useQuery } from "@tanstack/react-query";
import { cn } from "@/lib/utils";
import supabase from "@/lib/supabase";

interface BentoCardProps {
  className?: string;
  children: React.ReactNode;
  hover?: boolean;
}

const BentoCard = ({ className, children, hover = true }: BentoCardProps) => {
  return (
    <motion.div
      whileHover={hover ? { scale: 1.02 } : undefined}
      transition={{ type: "spring", stiffness: 300 }}
      className={className}
    >
      <Card className={cn(
        "group relative overflow-hidden h-full",
        "bg-gradient-to-br from-gray-900/90 via-gray-900/70 to-gray-800/50 backdrop-blur-xl",
        "border-gray-700/50 hover:border-emerald-500/50 transition-all duration-500",
        "hover:shadow-2xl hover:shadow-emerald-500/20",
      )}>
        {children}
      </Card>
    </motion.div>
  );
};

// Mini visualization component
const MiniChart = () => {
  const bars = [40, 70, 45, 90, 65, 85, 55];
  return (
    <div className="flex items-end gap-1 h-16">
      {bars.map((height, i) => (
        <motion.div
          key={i}
          initial={{ height: 0 }}
          animate={{ height: `${height}%` }}
          transition={{ delay: i * 0.1, duration: 0.5 }}
          className="w-2 bg-gradient-to-t from-emerald-500 to-emerald-300 rounded-t"
        />
      ))}
    </div>
  );
};

// Plant part icons
const plantPartIcons: Record<string, React.ReactNode> = {
  "Hemp Bast (Fiber)": <TreePine className="w-5 h-5" />,
  "Hemp Seed": <CircleIcon className="w-5 h-5" />,
  "Hemp Leaves": <Leaf className="w-5 h-5" />,
  "Hemp Flowers": <Sprout className="w-5 h-5" />,
};

export default function BentoGridV2() {
  const [activeCategory, setActiveCategory] = useState<string | null>(null);
  const [currentTime, setCurrentTime] = useState(new Date());

  // Update time every second
  useEffect(() => {
    const timer = setInterval(() => setCurrentTime(new Date()), 1000);
    return () => clearInterval(timer);
  }, []);

  // Fetch comprehensive statistics
  const { data: stats } = useQuery({
    queryKey: ['homepage-bento-stats'],
    queryFn: async () => {
      const [productsRes, companiesRes, plantPartsRes, researchRes] = await Promise.all([
        supabase.from('uses_products').select('*', { count: 'exact' }),
        supabase.from('hemp_companies').select('*'),
        supabase.from('plant_parts').select('*'),
        supabase.from('research_entries').select('*', { count: 'exact' }).limit(100)
      ]);
      
      const products = productsRes.data || [];
      const companies = companiesRes.data || [];
      const plantParts = plantPartsRes.data || [];
      const research = researchRes.data || [];
      
      // Calculate growth (mock data for demo)
      const growth = {
        products: "+12%",
        companies: "+8%",
        research: "+15%"
      };
      
      // Get plant part distribution
      const plantPartCounts = plantParts.map(part => ({
        name: part.name,
        count: products.filter(p => p.plant_part_id === part.id).length
      })).sort((a, b) => b.count - a.count);
      
      return {
        totalProducts: productsRes.count || products.length,
        totalCompanies: companies.length,
        totalPlantParts: plantParts.length,
        totalResearch: researchRes.count || research.length,
        growth,
        plantPartCounts,
        topPlantParts: plantPartCounts.slice(0, 4),
        recentProducts: products.slice(0, 5),
        featuredCompany: companies[0]
      };
    }
  });

  return (
    <section className="py-20 md:py-28 bg-gradient-to-b from-gray-950 via-gray-900 to-gray-950">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="inline-flex items-center gap-2 bg-emerald-500/10 border border-emerald-500/20 rounded-full px-4 py-2 mb-6"
          >
            <Sparkles className="w-4 h-4 text-emerald-400" />
            <span className="text-sm text-emerald-300">Discover the Hemp Ecosystem</span>
          </motion.div>
          
          <motion.h2 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="text-5xl md:text-6xl font-bold text-white mb-6"
          >
            Your Gateway to Hemp Innovation
          </motion.h2>
          
          <motion.p 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="text-xl text-gray-300 max-w-3xl mx-auto"
          >
            Explore {stats?.totalProducts || "700+"} products, {stats?.totalCompanies || "200+"} companies, 
            and cutting-edge research in the industrial hemp industry
          </motion.p>
        </div>

        {/* Bento Grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4 lg:gap-6 auto-rows-[minmax(180px,auto)]">
          
          {/* Hero Card - Product Explorer */}
          <BentoCard className="md:col-span-2 lg:col-span-2 md:row-span-2">
            <Link to="/products" className="block h-full p-8">
              <div className="flex flex-col h-full">
                <div className="flex items-start justify-between mb-6">
                  <div>
                    <h3 className="text-3xl font-bold text-white mb-2">Product Explorer</h3>
                    <p className="text-gray-300">Discover innovative hemp-based solutions</p>
                  </div>
                  <div className="text-right">
                    <div className="text-4xl font-bold text-emerald-400">{stats?.totalProducts || 714}</div>
                    <div className="text-sm text-emerald-300">{stats?.growth?.products || "+12%"} this month</div>
                  </div>
                </div>
                
                {/* Plant Part Distribution */}
                <div className="flex-grow">
                  <div className="grid grid-cols-2 gap-3 mb-6">
                    {stats?.topPlantParts?.map((part, idx) => (
                      <div key={idx} className="bg-gray-800/50 rounded-lg p-3 hover:bg-gray-800/70 transition-colors">
                        <div className="flex items-center gap-2 mb-1">
                          {plantPartIcons[part.name] || <Leaf className="w-4 h-4" />}
                          <span className="text-sm text-gray-300">{part.name}</span>
                        </div>
                        <div className="text-xl font-semibold text-white">{part.count}</div>
                      </div>
                    ))}
                  </div>
                </div>
                
                <Button size="lg" className="w-full bg-emerald-600 hover:bg-emerald-700 group">
                  Explore Products
                  <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                </Button>
              </div>
            </Link>
          </BentoCard>

          {/* Live Activity Feed */}
          <BentoCard className="md:col-span-1 lg:col-span-1 md:row-span-2">
            <div className="p-6 h-full flex flex-col">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-xl font-bold text-white">Live Activity</h3>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-emerald-400 rounded-full animate-pulse" />
                  <span className="text-xs text-gray-400">Live</span>
                </div>
              </div>
              
              <div className="flex-grow space-y-3 overflow-y-auto">
                <AnimatePresence mode="wait">
                  {stats?.recentProducts?.map((product, idx) => (
                    <motion.div
                      key={product.id}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: 20 }}
                      transition={{ delay: idx * 0.1 }}
                      className="bg-gray-800/30 rounded-lg p-3 border border-gray-700/50"
                    >
                      <div className="flex items-start gap-2">
                        <Package className="w-4 h-4 text-emerald-400 mt-0.5" />
                        <div className="flex-grow min-w-0">
                          <p className="text-sm text-white truncate">{product.name}</p>
                          <p className="text-xs text-gray-400">Added recently</p>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </AnimatePresence>
              </div>
              
              <div className="mt-4 pt-4 border-t border-gray-700/50">
                <p className="text-xs text-gray-400">{currentTime.toLocaleTimeString()}</p>
              </div>
            </div>
          </BentoCard>

          {/* Companies Hub */}
          <BentoCard className="md:col-span-1">
            <Link to="/admin?tab=companies" className="block h-full p-6">
              <div className="flex flex-col h-full">
                <Building2 className="w-10 h-10 text-blue-400 mb-4" />
                <h3 className="text-2xl font-bold text-white mb-2">Companies</h3>
                <p className="text-gray-300 mb-4 flex-grow">
                  Connect with {stats?.totalCompanies || 200}+ industry leaders
                </p>
                <div className="flex items-center justify-between">
                  <Badge variant="secondary" className="bg-blue-500/10 text-blue-400">
                    {stats?.growth?.companies || "+8%"} growth
                  </Badge>
                  <ArrowRight className="w-5 h-5 text-blue-400" />
                </div>
              </div>
            </Link>
          </BentoCard>

          {/* Research Center */}
          <BentoCard className="md:col-span-1">
            <Link to="/admin?tab=research" className="block h-full p-6">
              <div className="flex flex-col h-full">
                <FlaskConical className="w-10 h-10 text-purple-400 mb-4" />
                <h3 className="text-2xl font-bold text-white mb-2">Research</h3>
                <p className="text-gray-300 mb-4 flex-grow">
                  {stats?.totalResearch || 50}+ scientific studies
                </p>
                <div className="flex items-center justify-between">
                  <Badge variant="secondary" className="bg-purple-500/10 text-purple-400">
                    Latest findings
                  </Badge>
                  <ArrowRight className="w-5 h-5 text-purple-400" />
                </div>
              </div>
            </Link>
          </BentoCard>

          {/* Analytics Dashboard */}
          <BentoCard className="md:col-span-2 lg:col-span-1">
            <Link to="/admin?tab=analytics" className="block h-full p-6">
              <div className="flex flex-col h-full">
                <BarChart3 className="w-10 h-10 text-orange-400 mb-4" />
                <h3 className="text-xl font-bold text-white mb-3">Analytics</h3>
                <div className="flex-grow flex items-center justify-center">
                  <MiniChart />
                </div>
                <p className="text-sm text-gray-300 mt-3">View detailed insights →</p>
              </div>
            </Link>
          </BentoCard>

          {/* Smart Search */}
          <BentoCard className="md:col-span-1">
            <Link to="/products" className="block h-full p-6">
              <div className="relative h-full flex flex-col items-center justify-center text-center">
                <div className="absolute inset-0 bg-gradient-to-br from-emerald-500/10 to-cyan-500/10 opacity-50" />
                <Search className="w-12 h-12 text-emerald-400 mb-4" />
                <h3 className="text-xl font-bold text-white mb-2">Smart Search</h3>
                <p className="text-sm text-gray-300">AI-powered product discovery</p>
              </div>
            </Link>
          </BentoCard>

          {/* Featured Company */}
          {stats?.featuredCompany && (
            <BentoCard className="md:col-span-2 lg:col-span-2">
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <Badge variant="outline" className="border-amber-500/50 text-amber-400">
                    Featured Company
                  </Badge>
                  <Factory className="w-6 h-6 text-amber-400" />
                </div>
                <h3 className="text-2xl font-bold text-white mb-2">{stats.featuredCompany.name}</h3>
                <p className="text-gray-300 line-clamp-2">{stats.featuredCompany.description}</p>
                <Link 
                  to={`/admin?tab=companies`}
                  className="inline-flex items-center gap-2 text-amber-400 hover:text-amber-300 mt-4 text-sm font-medium"
                >
                  Learn more <ArrowRight className="w-4 h-4" />
                </Link>
              </div>
            </BentoCard>
          )}

          {/* Global Impact */}
          <BentoCard className="md:col-span-1" hover={false}>
            <div className="p-6 h-full flex flex-col items-center justify-center text-center relative">
              <div className="absolute inset-0 bg-gradient-to-br from-cyan-500/5 to-blue-500/5" />
              <Globe className="w-16 h-16 text-cyan-400 mb-4 animate-spin-slow" />
              <h3 className="text-lg font-bold text-white mb-2">Global Reach</h3>
              <p className="text-sm text-gray-300">Sustainable solutions worldwide</p>
            </div>
          </BentoCard>

        </div>

        {/* CTA Section */}
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="mt-12 text-center"
        >
          <p className="text-gray-400 mb-4">Ready to explore the future of sustainable materials?</p>
          <div className="flex flex-wrap gap-4 justify-center">
            <Button size="lg" variant="outline" className="border-emerald-500/50 hover:bg-emerald-500/10">
              <Search className="w-4 h-4 mr-2" />
              Search Database
            </Button>
            <Button size="lg" className="bg-emerald-600 hover:bg-emerald-700">
              <Package className="w-4 h-4 mr-2" />
              Browse All Products
            </Button>
          </div>
        </motion.div>
      </div>
    </section>
  );
}