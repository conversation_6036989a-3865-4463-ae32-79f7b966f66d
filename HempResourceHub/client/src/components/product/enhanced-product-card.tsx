import { <PERSON> } from "wouter";
import { HempProduct, PlantPart, Industry } from "@shared/schema";
import { Badge } from "@/components/ui/badge";
import { ArrowRight, Leaf, TreePine, Factory, Building2, CheckCircle, Sparkles } from "lucide-react";
import { useState } from "react";

interface EnhancedProductCardProps {
  product: HempProduct & { image_url?: string };
  plantParts?: PlantPart[];
  industries?: Industry[];
  viewMode?: 'grid' | 'list' | 'mobile';
}

const EnhancedProductCard = ({ product, plantParts, industries, viewMode = 'grid' }: EnhancedProductCardProps) => {
  const [imageError, setImageError] = useState(false);
  
  // Extract stage from product properties
  const stage = product.commercializationStage || 'Research';
  
  const stageColors = {
    'Growing': 'bg-hemp-500/20 text-hemp-400 border-hemp-500/50',
    'Established': 'bg-secondary-500/20 text-secondary-400 border-secondary-500/50',
    'Research': 'bg-secondary-500/20 text-secondary-400 border-secondary-500/50',
    'Speculative': 'bg-orange-500/20 text-orange-400 border-orange-500/50'
  };
  
  // Get plant part and industry names
  const plantPart = plantParts?.find(p => p.id === product.plantPartId);
  const industry = industries?.find(i => i.id === product.industrySubCategoryId);
  
  // Get category-specific fallback image
  const getFallbackImage = () => {
    const fallbacks: Record<string, string> = {
      'Hemp Seed': '/images/fallbacks/hemp-seeds.jpg',
      'Hemp Bast (Fiber)': '/images/fallbacks/hemp-fiber.jpg',
      'Hemp Flowers': '/images/fallbacks/hemp-flower.jpg',
      'Hemp Roots': '/images/fallbacks/hemp-root.jpg',
      'Hemp Hurd (Shivs)': '/images/fallbacks/hemp-hurd.jpg',
      'Hemp Leaves': '/images/fallbacks/hemp-leaves.jpg'
    };
    return fallbacks[plantPart?.name || ''] || '/images/unknown-hemp-image.png';
  };
  
  // Support both naming conventions from API
  const productImageUrl = product.imageUrl || product.image_url;
  const imageUrl = imageError ? getFallbackImage() : (productImageUrl || getFallbackImage());
  const benefits = product.benefits_advantages || [];
  const isAIGenerated = productImageUrl?.includes('replicate') || productImageUrl?.includes('dall-e');
  
  // Mobile view - compact horizontal layout
  if (viewMode === 'mobile') {
    return (
      <Link href={`/product/${product.id}`}>
        <div className="group bg-gray-900/40 backdrop-blur-sm rounded-xl p-3 border border-gray-800 hover:border-hemp-500/30 transition-all duration-300 hover:shadow-lg hover:shadow-hemp-500/10">
          <div className="flex gap-3">
            {/* Thumbnail */}
            <div className="w-20 h-20 rounded-lg overflow-hidden bg-gray-800 flex-shrink-0">
              <img
                src={imageUrl}
                alt={product.name}
                className="w-full h-full object-cover"
                onError={() => setImageError(true)}
              />
            </div>
            
            {/* Content */}
            <div className="flex-1 min-w-0">
              <h3 className="font-semibold text-sm line-clamp-2 mb-1">{product.name}</h3>
              
              {/* Tags */}
              <div className="flex gap-1 mb-2 flex-wrap">
                {plantPart && (
                  <Badge variant="plantPart" className="text-xs">
                    <TreePine className="w-3 h-3 mr-1" />
                    {plantPart.name}
                  </Badge>
                )}
                {industry && (
                  <Badge variant="industry" className="text-xs">
                    <Factory className="w-3 h-3 mr-1" />
                    {industry.name}
                  </Badge>
                )}
              </div>
              
              {/* Benefits preview */}
              {benefits.length > 0 && (
                <div className="text-xs text-gray-400 line-clamp-1">
                  {benefits[0]}
                </div>
              )}
            </div>
            
            {/* Arrow */}
            <ArrowRight className="w-4 h-4 text-gray-500 flex-shrink-0 self-center" />
          </div>
        </div>
      </Link>
    );
  }
  
  // List view
  if (viewMode === 'list') {
    return (
      <Link href={`/product/${product.id}`}>
        <div className="group bg-gray-900/40 backdrop-blur-sm rounded-xl p-4 cursor-pointer transition-all duration-300 hover:bg-gray-800/50 border border-gray-800 hover:border-hemp-500/30 hover:shadow-hemp-500/10">
          <div className="flex items-center gap-4">
            {/* Image - Larger for better visibility */}
            <div className="w-32 h-32 flex-shrink-0 rounded-lg overflow-hidden bg-gray-800 relative">
              <img
                src={imageUrl}
                alt={product.name}
                className="w-full h-full object-cover"
                onError={() => setImageError(true)}
              />
              {isAIGenerated && (
                <Badge className="absolute top-2 right-2 bg-purple-500/20 backdrop-blur-sm text-purple-300 border-0 text-xs">
                  <Sparkles className="w-3 h-3" />
                </Badge>
              )}
            </div>
            
            {/* Content */}
            <div className="flex-1 min-w-0">
              <div className="flex items-start justify-between gap-4 mb-2">
                <h3 className="hemp-brand-ultra font-semibold text-lg">
                  {product.name}
                </h3>
                <Badge className={`${stageColors[stage as keyof typeof stageColors] || stageColors.Research} flex-shrink-0`}>
                  {stage}
                </Badge>
              </div>
              
              {/* Benefits preview */}
              {benefits.length > 0 && (
                <div className="mb-3">
                  <div className="flex flex-wrap gap-1">
                    {benefits.slice(0, 3).map((benefit, idx) => (
                      <Badge key={idx} variant="outline" className="text-xs border-gray-700">
                        {benefit.length > 30 ? `${benefit.substring(0, 30)}...` : benefit}
                      </Badge>
                    ))}
                    {benefits.length > 3 && (
                      <Badge variant="outline" className="text-xs border-gray-700 text-green-400">
                        +{benefits.length - 3} more
                      </Badge>
                    )}
                  </div>
                </div>
              )}
              
              <div className="flex items-center gap-4 text-xs">
                {plantPart && (
                  <div className="flex items-center gap-1 text-gray-500">
                    <TreePine className="w-3 h-3" />
                    <span>{plantPart.name}</span>
                  </div>
                )}
                {industry && (
                  <div className="flex items-center gap-1 text-gray-500">
                    <Factory className="w-3 h-3" />
                    <span>{industry.name}</span>
                  </div>
                )}
                {(product as any).hemp_company_products && (product as any).hemp_company_products.length > 0 && (
                  <div className="flex items-center gap-1 text-gray-500">
                    <Building2 className="w-3 h-3" />
                    <span>
                      {(product as any).hemp_company_products[0].hemp_companies.name}
                      {(product as any).hemp_company_products.length > 1 && ` +${(product as any).hemp_company_products.length - 1}`}
                    </span>
                  </div>
                )}
                <ArrowRight className="w-4 h-4 text-gray-500 group-hover:text-green-400 transition-colors ml-auto" />
              </div>
            </div>
          </div>
        </div>
      </Link>
    );
  }
  
  // Grid view (default)
  return (
    <Link href={`/product/${product.id}`}>
      <div className="group bg-gray-900/40 backdrop-blur-sm rounded-xl overflow-hidden cursor-pointer transition-all duration-300 hover:shadow-2xl hover:shadow-green-500/20 hover:-translate-y-1 border border-gray-800/50 hover:border-green-500/30">
        {/* Image - Larger aspect ratio for better visibility */}
        <div className="aspect-[3/2] relative overflow-hidden bg-gray-800">
          <img
            src={imageUrl}
            alt={product.name}
            className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
            onError={() => setImageError(true)}
          />
          {/* Gradient overlay */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent" />

          {/* Stage badge */}
          <div className="absolute top-3 left-3">
            <Badge className={`${stageColors[stage as keyof typeof stageColors] || stageColors.Research} backdrop-blur-sm border text-xs`}>
              {stage}
            </Badge>
          </div>
          
          {/* AI Generated badge */}
          {isAIGenerated && (
            <Badge className="absolute top-3 right-3 bg-purple-500/20 backdrop-blur-sm text-purple-300 border-0 text-xs">
              <Sparkles className="w-3 h-3 mr-1" />
              AI
            </Badge>
          )}

          {/* Product name overlay on image */}
          <div className="absolute bottom-3 left-3 right-3">
            <h3 className="hemp-brand-ultra font-semibold text-lg line-clamp-2">
              {product.name}
            </h3>
          </div>
        </div>
        
        {/* Content with Benefits */}
        <div className="p-4">
          {/* Benefits preview */}
          {benefits.length > 0 && (
            <div className="mb-3">
              <div className="flex flex-wrap gap-1">
                {benefits.slice(0, 2).map((benefit, idx) => (
                  <Badge key={idx} variant="outline" className="text-xs border-gray-700 text-gray-300">
                    {benefit.length > 25 ? `${benefit.substring(0, 25)}...` : benefit}
                  </Badge>
                ))}
                {benefits.length > 2 && (
                  <Badge variant="outline" className="text-xs border-gray-700 text-green-400">
                    +{benefits.length - 2} more
                  </Badge>
                )}
              </div>
            </div>
          )}
          
          {/* Category badges and arrow */}
          <div className="flex items-center justify-between">
            <div className="flex flex-wrap gap-1">
              {plantPart && (
                <Badge className="bg-green-500/20 text-green-400 border-0 text-xs">
                  <TreePine className="w-3 h-3 mr-1" />
                  {plantPart.name}
                </Badge>
              )}
              {industry && (
                <Badge className="bg-blue-500/20 text-blue-400 border-0 text-xs">
                  <Factory className="w-3 h-3 mr-1" />
                  {industry.name}
                </Badge>
              )}
            </div>

            {/* Arrow icon */}
            <ArrowRight className="w-4 h-4 text-gray-500 group-hover:text-green-400 transition-colors flex-shrink-0" />
          </div>
        </div>
      </div>
    </Link>
  );
};

export default EnhancedProductCard;