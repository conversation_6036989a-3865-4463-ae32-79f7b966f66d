import React, { useState, useRef } from "react";
import { <PERSON> } from "wouter";
import { HempProduct } from "@shared/schema";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { mobileClasses } from "@/utils/mobile-optimization";
import {
  ArrowRight,
  Heart,
  Share2,
  Bookmark,
  Eye,
  TrendingUp,
  Star,
  ExternalLink,
  Info,
  Zap,
  Leaf,
  Factory
} from "lucide-react";
import { cn } from "@/lib/utils";
import { OptimizedImage } from "@/components/ui/optimized-image";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface InteractiveProductCardProps {
  product: HempProduct & { image_url?: string };
  industryNames: Record<number, string>;
  subIndustryNames: Record<number, string>;
  plantPartNames: Record<number, string>;
  variant?: 'default' | 'compact' | 'featured';
  showActions?: boolean;
  showStats?: boolean;
  onFavorite?: (productId: number) => void;
  onShare?: (product: HempProduct & { image_url?: string }) => void;
  onBookmark?: (productId: number) => void;
  isFavorited?: boolean;
  isBookmarked?: boolean;
  className?: string;
}

export function InteractiveProductCard({
  product,
  industryNames,
  subIndustryNames,
  plantPartNames,
  variant = 'default',
  showActions = true,
  showStats = false,
  onFavorite,
  onShare,
  onBookmark,
  isFavorited = false,
  isBookmarked = false,
  className
}: InteractiveProductCardProps) {
  const [isHovered, setIsHovered] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);
  const cardRef = useRef<HTMLDivElement>(null);

  const handleFavorite = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    onFavorite?.(product.id);
  };

  const handleShare = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    onShare?.(product);
  };

  const handleBookmark = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    onBookmark?.(product.id);
  };

  const getSustainabilityScore = () => {
    // Mock sustainability score based on product attributes
    const benefits = product.benefitsAdvantages?.length || 0;
    const environmental = product.environmental_impact ? 1 : 0;
    return Math.min(5, Math.floor((benefits + environmental) / 2) + 3);
  };

  const getCardVariantStyles = () => {
    switch (variant) {
      case 'compact':
        return "h-80"; // Increased for larger image display
      case 'featured':
        return "h-[28rem] md:h-96"; // Much larger for featured cards
      default:
        return "h-96"; // Increased default height for image-focused design
    }
  };

  const industryName = industryNames[product.industrySubCategoryId];
  const subIndustryName = subIndustryNames[product.industrySubCategoryId || 0];
  const plantPartName = plantPartNames[product.plantPartId];
  const sustainabilityScore = getSustainabilityScore();

  return (
    <Card
      ref={cardRef}
      className={cn(
        "group relative overflow-hidden cursor-pointer transition-all duration-300",
        "bg-gray-900/40 backdrop-blur-sm border-gray-800/50",
        "hover:shadow-2xl hover:shadow-hemp-500/20 hover:-translate-y-2",
        "hover:border-hemp-500/30 hover:bg-gray-900/60",
        getCardVariantStyles(),
        className
      )}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <Link href={`/product/${product.id}`}>
        <div className="relative h-full flex flex-col">
          {/* Image Section - Now takes up much more space */}
          <div className="relative flex-[4] overflow-hidden">
            <OptimizedImage
              src={(product.imageUrl || product.image_url) || '/images/unknown-hemp-image.png'}
              alt={product.name}
              className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
              onLoad={() => setImageLoaded(true)}
              fallbackSrc="/images/unknown-hemp-image.png"
            />
            
            {/* Gradient Overlays */}
            <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent" />
            <div className={cn(
              "absolute inset-0 bg-gradient-to-t from-hemp-900/20 to-transparent opacity-0 transition-opacity duration-300",
              isHovered && "opacity-100"
            )} />

            {/* Top Badges */}
            <div className="absolute top-3 left-3 flex flex-wrap gap-2">
              {industryName && (
                <Badge variant="industry" className="backdrop-blur-sm text-xs">
                  <Factory className="w-3 h-3 mr-1" />
                  {industryName}
                </Badge>
              )}
              {product.commercializationStage && (
                <Badge 
                  variant="outline" 
                  className="bg-green-500/20 text-green-400 border-green-500/30 text-xs"
                >
                  <Zap className="w-3 h-3 mr-1" />
                  {product.commercializationStage}
                </Badge>
              )}
            </div>

            {/* Action Buttons */}
            {showActions && (
              <div className={cn(
                "absolute top-3 right-3 flex flex-col gap-2 opacity-0 transition-all duration-300",
                isHovered && "opacity-100"
              )}>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      size="sm"
                      variant="ghost"
                      className={cn(
                        "h-8 w-8 md:h-8 md:w-8 p-0 bg-black/50 backdrop-blur-sm hover:bg-black/70",
                        mobileClasses.touchTarget,
                        isFavorited && "text-red-400 hover:text-red-300"
                      )}
                      onClick={handleFavorite}
                      aria-label={isFavorited ? "Remove from favorites" : "Add to favorites"}
                    >
                      <Heart className={cn("h-4 w-4", isFavorited && "fill-current")} />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    {isFavorited ? "Remove from favorites" : "Add to favorites"}
                  </TooltipContent>
                </Tooltip>

                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      size="sm"
                      variant="ghost"
                      className={cn(
                        "h-8 w-8 md:h-8 md:w-8 p-0 bg-black/50 backdrop-blur-sm hover:bg-black/70",
                        mobileClasses.touchTarget,
                        isBookmarked && "text-blue-400 hover:text-blue-300"
                      )}
                      onClick={handleBookmark}
                      aria-label={isBookmarked ? "Remove bookmark" : "Add bookmark"}
                    >
                      <Bookmark className={cn("h-4 w-4", isBookmarked && "fill-current")} />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    {isBookmarked ? "Remove bookmark" : "Bookmark"}
                  </TooltipContent>
                </Tooltip>

                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      size="sm"
                      variant="ghost"
                      className={cn(
                        "h-8 w-8 md:h-8 md:w-8 p-0 bg-black/50 backdrop-blur-sm hover:bg-black/70",
                        mobileClasses.touchTarget
                      )}
                      onClick={handleShare}
                      aria-label="Share product"
                    >
                      <Share2 className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Share product</TooltipContent>
                </Tooltip>
              </div>
            )}

            {/* Product Name Overlay */}
            <div className="absolute bottom-3 left-3 right-3">
              <h3 className="hemp-brand-ultra font-semibold text-lg line-clamp-2 leading-tight">
                {product.name}
              </h3>
            </div>
          </div>

          {/* Ultra-Minimal Content Section - Just essential info */}
          <CardContent className="p-2 flex-shrink-0 bg-black/60 backdrop-blur-sm">
            {/* Compact Tags and Arrow */}
            <div className="flex items-center justify-between">
              <div className="flex gap-1 flex-1 min-w-0">
                {plantPartName && (
                  <Badge variant="plantPart" className="text-xs px-1.5 py-0.5">
                    <Leaf className="w-2.5 h-2.5 mr-1" />
                    <span className="truncate">{plantPartName}</span>
                  </Badge>
                )}
                {subIndustryName && (
                  <Badge variant="industry" className="text-xs px-1.5 py-0.5">
                    <span className="truncate">{subIndustryName}</span>
                  </Badge>
                )}
              </div>

              <ArrowRight className="w-3.5 h-3.5 text-gray-500 group-hover:text-hemp-400 group-hover:translate-x-1 transition-all flex-shrink-0 ml-2" />
            </div>
          </CardContent>
        </div>
      </Link>
    </Card>
  );
}
