import { Link } from "wouter";
import { HempProduct } from "@shared/schema";
import { Badge } from "@/components/ui/badge";
import { ArrowRight } from "lucide-react";

interface ModernProductCardProps {
  product: HempProduct & { image_url?: string };
  industryName?: string;
  subIndustryName?: string;
  plantPartName?: string;
}

const ModernProductCard = ({
  product,
  industryName,
  subIndustryName,
  plantPartName,
}: ModernProductCardProps) => {
  return (
    <Link href={`/product/${product.id}`}>
      <div className="group bg-gray-900/40 backdrop-blur-sm rounded-xl overflow-hidden cursor-pointer transition-all duration-300 hover:shadow-xl hover:shadow-hemp-500/10 hover:-translate-y-1 border border-gray-800 hover:border-hemp-500/30">
        {/* Image */}
        <div className="aspect-[4/3] relative overflow-hidden bg-gray-800">
          <img
            src={(product.imageUrl || product.image_url) || '/images/unknown-hemp-image.png'}
            alt={product.name}
            className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
            onError={(e) => {
              e.currentTarget.src = '/images/unknown-hemp-image.png';
            }}
          />
          {/* Gradient overlay */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />
          
          {/* Category badge */}
          {industryName && (
            <div className="absolute top-3 left-3">
              <Badge variant="industry" className="backdrop-blur-sm">
                {industryName}
              </Badge>
            </div>
          )}
        </div>
        
        {/* Content */}
        <div className="p-5">
          <h3 className="hemp-brand-ultra font-semibold text-lg mb-4">
            {product.name}
          </h3>
          
          {/* Tags */}
          <div className="flex items-center justify-between">
            <div className="flex gap-2 flex-wrap">
              {plantPartName && (
                <Badge variant="plantPart" className="text-xs">
                  {plantPartName}
                </Badge>
              )}
              {subIndustryName && (
                <Badge variant="industry" className="text-xs">
                  {subIndustryName}
                </Badge>
              )}
            </div>

            {/* Arrow icon */}
            <ArrowRight className="w-4 h-4 text-gray-500 group-hover:text-hemp-400 transition-colors" />
          </div>
        </div>
      </div>
    </Link>
  );
};

export default ModernProductCard;