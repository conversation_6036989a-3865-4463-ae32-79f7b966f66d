import { Link } from "wouter";
import { HempProduct } from "@shared/schema";
import { Badge } from "@/components/ui/badge";
import { ArrowRight } from "lucide-react";

interface ProductCardProps {
  product: HempProduct & { image_url?: string };
  industryNames?: Record<number, string>;
  subIndustryNames?: Record<number, string>;
  plantPartNames?: Record<number, string>;
}

const ProductCard = ({
  product,
  industryNames,
  subIndustryNames,
  plantPartNames,
}: ProductCardProps) => {
  return (
    <Link href={`/product/${product.id}`}>
      <div className="group bg-gray-900/40 backdrop-blur-sm rounded-xl overflow-hidden cursor-pointer transition-all duration-300 hover:shadow-2xl hover:shadow-hemp-500/20 hover:-translate-y-1 border border-gray-800/50 hover:border-hemp-500/30 card-hover scale-click">
        {/* Image */}
        <div className="aspect-[4/3] relative overflow-hidden bg-gray-800 image-hover">
          <img
            src={product.imageUrl || product.image_url || '/images/unknown-hemp-image.png'}
            alt={product.name}
            className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
            onError={(e) => {
              e.currentTarget.src = '/images/unknown-hemp-image.png';
            }}
          />
          {/* Gradient overlay */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />
          
          {/* Category badge */}
          {industryNames && industryNames[product.industrySubCategoryId] && (
            <div className="absolute top-3 left-3">
              <Badge variant="industry" className="backdrop-blur-sm text-xs">
                {industryNames[product.industrySubCategoryId]}
              </Badge>
            </div>
          )}
        </div>
        
        {/* Content */}
        <div className="p-5">
          <h3 className="hemp-brand-ultra font-semibold text-lg mb-4">
            {product.name}
          </h3>
          
          {/* Tags and Arrow */}
          <div className="flex items-center justify-between">
            <div className="flex gap-2 flex-wrap">
              {plantPartNames && plantPartNames[product.plantPartId] && (
                <Badge variant="plantPart" className="text-xs">
                  {plantPartNames[product.plantPartId]}
                </Badge>
              )}
              {subIndustryNames && subIndustryNames[product.industrySubCategoryId || 0] && (
                <Badge variant="industry" className="text-xs">
                  {subIndustryNames[product.industrySubCategoryId || 0]}
                </Badge>
              )}
            </div>
            
            {/* Arrow icon */}
            <ArrowRight className="w-4 h-4 text-gray-500 group-hover:text-green-400 transition-colors flex-shrink-0" />
          </div>
        </div>
      </div>
    </Link>
  );
};

export default ProductCard;