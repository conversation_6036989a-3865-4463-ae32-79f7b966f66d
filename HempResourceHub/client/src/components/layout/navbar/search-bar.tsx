import { SmartSearch } from "@/components/ui/smart-search";
import { useLocation } from "wouter";

interface SearchBarProps {
  className?: string;
  onSearch?: () => void;
}

export const SearchBar = ({ className, onSearch }: SearchBarProps) => {
  const [, setLocation] = useLocation();

  const handleSearch = (query: string) => {
    setLocation(`/products?search=${encodeURIComponent(query)}`);
    onSearch?.();
  };

  return (
    <SmartSearch
      className={className}
      onSearch={handleSearch}
      showAISuggestions={true}
      showVoiceSearch={true}
      showImageSearch={false}
      placeholder="Search products, companies, research..."
    />
  );
};

export const ProminentSearchBar = () => {
  const [location, setLocation] = useLocation();
  const isHomePage = location === "/";

  const handleSearch = (query: string) => {
    setLocation(`/products?search=${encodeURIComponent(query)}`);
  };

  return (
    <div className="w-full max-w-3xl mx-auto">
      <SmartSearch
        className="w-full h-14 text-xl shadow-2xl hover:shadow-3xl focus-within:shadow-3xl transition-all duration-300 border-2 border-secondary-400/40 hover:border-secondary-400/60 focus-within:border-secondary-400 bg-gray-900/80 backdrop-blur"
        onSearch={handleSearch}
        showAISuggestions={true}
        showVoiceSearch={true}
        showImageSearch={false}
        placeholder="Search the hemp database..."
        autoFocus={isHomePage}
      />
    </div>
  );
};