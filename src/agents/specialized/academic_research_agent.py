#!/usr/bin/env python3
"""
Academic Research Agent - Mines PubMed and academic papers for hemp products
Part of the Hemp Database Expansion Initiative
"""

import json
import time
import re
import random
from datetime import datetime
from typing import Dict, List, Optional, Tuple
import psycopg2
import os
import requests
from urllib.parse import quote

class AcademicResearchAgent:
    """Mines academic research papers for hemp product discoveries"""
    
    def __init__(self):
        self.name = "Academic Research Agent"
        self.version = "1.0.0"
        self.discovered_count = 0
        self.database_url = os.environ.get('DATABASE_URL')
        
        # PubMed API configuration
        self.pubmed_base = "https://eutils.ncbi.nlm.nih.gov/entrez/eutils/"
        self.email = "<EMAIL>"  # Required by NCBI
        
        # Research topics to explore
        self.research_topics = [
            "hemp fiber composite materials",
            "hemp bioplastic development",
            "hemp pharmaceutical applications",
            "hemp protein extraction",
            "hemp cellulose nanofibers",
            "hemp oil biomedical uses",
            "hemp construction materials",
            "hemp textile innovations",
            "hemp supercapacitor electrodes",
            "hemp water filtration",
            "hemp food products development",
            "hemp cosmetic formulations",
            "hemp agricultural applications",
            "hemp environmental remediation",
            "hemp nanotechnology applications",
            "hemp biochar production",
            "hemp paper manufacturing",
            "hemp adhesive development",
            "hemp acoustic materials",
            "hemp antimicrobial properties"
        ]
        
        # Product templates for research findings
        self.product_templates = {
            "material": "Hemp-Based {} Material",
            "compound": "Hemp-Derived {} Compound",
            "process": "Hemp {} Processing Method",
            "formulation": "Hemp {} Formulation",
            "application": "Hemp {} Application",
            "technology": "Hemp {} Technology",
            "system": "Hemp-Based {} System",
            "product": "Hemp {} Product"
        }
        
    def search_pubmed(self, query: str, max_results: int = 10) -> List[Dict]:
        """Search PubMed for hemp research papers"""
        try:
            # Search for IDs
            search_url = f"{self.pubmed_base}esearch.fcgi"
            search_params = {
                "db": "pubmed",
                "term": query,
                "retmax": max_results,
                "retmode": "json",
                "email": self.email
            }
            
            response = requests.get(search_url, params=search_params)
            if response.status_code != 200:
                print(f"PubMed search failed: {response.status_code}")
                return []
            
            search_data = response.json()
            id_list = search_data.get("esearchresult", {}).get("idlist", [])
            
            if not id_list:
                return []
            
            # Fetch paper details
            fetch_url = f"{self.pubmed_base}efetch.fcgi"
            fetch_params = {
                "db": "pubmed",
                "id": ",".join(id_list),
                "retmode": "xml",
                "rettype": "abstract",
                "email": self.email
            }
            
            response = requests.get(fetch_url, params=fetch_params)
            if response.status_code != 200:
                print(f"PubMed fetch failed: {response.status_code}")
                return []
            
            # Parse XML response (simplified)
            papers = self.parse_pubmed_xml(response.text)
            return papers
            
        except Exception as e:
            print(f"Error searching PubMed: {e}")
            return []
    
    def parse_pubmed_xml(self, xml_text: str) -> List[Dict]:
        """Parse PubMed XML response (simplified)"""
        papers = []
        
        # Simple regex parsing (in production, use proper XML parser)
        articles = re.findall(r'<PubmedArticle>(.*?)</PubmedArticle>', xml_text, re.DOTALL)
        
        for article in articles[:5]:  # Limit to 5 papers per search
            paper = {}
            
            # Extract title
            title_match = re.search(r'<ArticleTitle>(.*?)</ArticleTitle>', article)
            if title_match:
                paper['title'] = re.sub(r'<[^>]+>', '', title_match.group(1))
            
            # Extract abstract
            abstract_match = re.search(r'<AbstractText[^>]*>(.*?)</AbstractText>', article)
            if abstract_match:
                paper['abstract'] = re.sub(r'<[^>]+>', '', abstract_match.group(1))[:500]
            
            # Extract PMID
            pmid_match = re.search(r'<PMID[^>]*>(\d+)</PMID>', article)
            if pmid_match:
                paper['pmid'] = pmid_match.group(1)
            
            # Extract year
            year_match = re.search(r'<Year>(\d{4})</Year>', article)
            if year_match:
                paper['year'] = year_match.group(1)
            
            if 'title' in paper and 'abstract' in paper:
                papers.append(paper)
        
        return papers
    
    def generate_products_from_paper(self, paper: Dict, topic: str) -> List[Dict]:
        """Generate products from research paper"""
        products = []
        
        title = paper.get('title', '')
        abstract = paper.get('abstract', '')
        pmid = paper.get('pmid', 'Unknown')
        
        # Extract key findings and applications
        findings = self.extract_findings(title + " " + abstract)
        
        for finding in findings[:3]:  # Max 3 products per paper
            # Determine product type
            product_type = self.determine_product_type(finding)
            template = self.product_templates.get(product_type, "Hemp {} Innovation")
            
            # Generate product name with variation
            base_name = template.format(finding['keyword'])
            
            # Add variation based on paper year and context
            year = paper.get('year', '2023')
            if int(year) >= 2022:
                base_name = f"Advanced {base_name}"
            elif int(year) >= 2020:
                base_name = f"Novel {base_name}"
            
            # Clean up any double spaces
            product_name = ' '.join(base_name.split())
            
            # Generate description
            description = self.create_research_description(
                product_name, title, abstract, pmid, finding
            )
            
            # Generate specifications
            tech_specs = self.generate_research_specs(finding, paper)
            
            # Generate benefits
            benefits = self.generate_research_benefits(finding)
            
            # Map to industry
            industry_id = self.map_finding_to_industry(finding)
            plant_part_id = self.map_finding_to_plant_part(finding)
            
            # Calculate quality score
            quality_score = self.calculate_quality_score(
                product_name, description, benefits, tech_specs
            )
            
            product = {
                'name': product_name,
                'description': description,
                'research_id': f"PMID:{pmid}",
                'plant_part_id': plant_part_id,
                'industry_id': industry_id,
                'benefits': benefits,
                'technical_specifications': tech_specs,
                'source_agent': self.name,
                'confidence_score': 0.75 + random.random() * 0.15,
                'quality_score': quality_score
            }
            
            products.append(product)
        
        return products
    
    def extract_findings(self, text: str) -> List[Dict]:
        """Extract key findings from research text"""
        findings = []
        text_lower = text.lower()
        
        # Keywords indicating product potential
        product_keywords = {
            'composite': 'material',
            'fiber': 'material',
            'extract': 'compound',
            'protein': 'compound',
            'oil': 'compound',
            'nanoparticle': 'technology',
            'membrane': 'technology',
            'coating': 'application',
            'formulation': 'formulation',
            'biodegradable': 'material',
            'antimicrobial': 'application',
            'pharmaceutical': 'formulation',
            'therapeutic': 'application',
            'sustainable': 'technology',
            'biochar': 'material',
            'cellulose': 'material',
            'polymer': 'material',
            'catalyst': 'technology',
            'adsorbent': 'material',
            'electrode': 'technology'
        }
        
        for keyword, prod_type in product_keywords.items():
            if keyword in text_lower:
                # Extract context around keyword
                pattern = rf'\b(\w+\s+)?{keyword}(\s+\w+)?\b'
                matches = re.findall(pattern, text_lower)
                
                for match in matches[:2]:  # Limit matches
                    # Clean up the match - remove extra spaces
                    prefix = match[0].strip() if match[0] else ''
                    suffix = match[1].strip() if match[1] else ''
                    
                    # Build context with proper spacing
                    parts = [prefix, keyword, suffix]
                    context = ' '.join(p for p in parts if p)
                    
                    if context and len(context) > len(keyword):
                        findings.append({
                            'keyword': context.title(),
                            'type': prod_type,
                            'original_keyword': keyword
                        })
        
        # Remove duplicates
        unique_findings = []
        seen = set()
        for finding in findings:
            key = finding['keyword']
            if key not in seen:
                seen.add(key)
                unique_findings.append(finding)
        
        return unique_findings[:5]  # Max 5 findings
    
    def determine_product_type(self, finding: Dict) -> str:
        """Determine product type from finding"""
        return finding.get('type', 'product')
    
    def create_research_description(self, name: str, title: str, abstract: str,
                                  pmid: str, finding: Dict) -> str:
        """Create product description from research"""
        # Start with varied opening
        openers = [
            f"{name} represents a breakthrough innovation from ",
            f"Developed through rigorous research, {name} emerges from ",
            f"{name} is an advanced solution derived from ",
            f"Scientific validation supports {name}, originating from "
        ]
        desc = random.choice(openers) + f"the study '{title}'. "
        
        # Add unique research insights
        prod_type = finding.get('type', 'product')
        keyword = finding.get('keyword', '').lower()
        
        # Type-specific descriptions with variations
        if prod_type == 'material':
            material_descs = [
                f"This {keyword} demonstrates exceptional mechanical properties, ",
                f"The {keyword} exhibits superior performance characteristics, ",
                f"Revolutionary {keyword} offers unprecedented strength and durability, "
            ]
            desc += random.choice(material_descs)
        elif prod_type == 'compound':
            compound_descs = [
                f"This bioactive {keyword} shows remarkable therapeutic properties, ",
                f"The {keyword} compound demonstrates significant biological activity, ",
                f"Natural {keyword} extract provides powerful health benefits, "
            ]
            desc += random.choice(compound_descs)
        elif prod_type == 'technology':
            tech_descs = [
                f"This cutting-edge {keyword} technology enables new possibilities, ",
                f"Advanced {keyword} systems provide innovative solutions, ",
                f"State-of-the-art {keyword} technology revolutionizes the field, "
            ]
            desc += random.choice(tech_descs)
        else:
            desc += f"This innovative {keyword} opens new applications in the industry, "
        
        # Add specific abstract insights
        if abstract:
            # Extract key findings from abstract
            key_points = abstract.split('. ')[:2]
            if key_points:
                desc += "with research showing " + key_points[0].lower() + ". "
        
        # Add unique closing with reference
        closings = [
            f"Peer-reviewed research validates effectiveness (PMID:{pmid}).",
            f"Scientific evidence supports commercial viability (Reference: PMID:{pmid}).",
            f"Academic validation ensures quality and reliability (Study ID: PMID:{pmid})."
        ]
        desc += random.choice(closings)
        
        return desc
    
    def generate_research_specs(self, finding: Dict, paper: Dict) -> Dict:
        """Generate technical specifications from research"""
        specs = {
            "research_id": f"PMID:{paper.get('pmid', 'Unknown')}",
            "publication_year": paper.get('year', '2023'),
            "research_stage": "Laboratory/Pilot",
            "validation": "Peer-reviewed"
        }
        
        # Type-specific specs
        prod_type = finding.get('type', 'product')
        if prod_type == 'material':
            specs.update({
                "material_type": finding.get('original_keyword', 'composite'),
                "processing_method": "Advanced manufacturing",
                "scalability": "Demonstrated"
            })
        elif prod_type == 'compound':
            specs.update({
                "compound_type": finding.get('original_keyword', 'extract'),
                "extraction_method": "Optimized process",
                "purity": "Research grade"
            })
        elif prod_type == 'technology':
            specs.update({
                "technology_type": finding.get('original_keyword', 'process'),
                "efficiency": "High",
                "innovation_level": "Novel"
            })
        
        return specs
    
    def generate_research_benefits(self, finding: Dict) -> List[str]:
        """Generate benefits from research findings"""
        base_benefits = [
            "Scientifically validated through peer-reviewed research",
            "Evidence-based product development",
            "Academic institution backing"
        ]
        
        # Type-specific benefits
        type_benefits = {
            'material': [
                "Superior material properties demonstrated",
                "Sustainable alternative to conventional materials"
            ],
            'compound': [
                "Bioactive properties scientifically proven",
                "Natural compound with therapeutic potential"
            ],
            'technology': [
                "Cutting-edge technological innovation",
                "Efficiency improvements validated"
            ],
            'formulation': [
                "Optimized formulation through research",
                "Enhanced bioavailability demonstrated"
            ],
            'application': [
                "Novel application scientifically validated",
                "Cross-industry potential identified"
            ]
        }
        
        prod_type = finding.get('type', 'product')
        if prod_type in type_benefits:
            base_benefits.extend(type_benefits[prod_type])
        
        return base_benefits[:5]
    
    def map_finding_to_industry(self, finding: Dict) -> int:
        """Map research finding to industry"""
        keyword = finding.get('original_keyword', '').lower()
        
        # Keyword to industry mapping
        industry_map = {
            'composite': 6,      # Materials
            'fiber': 2,          # Textiles
            'pharmaceutical': 4, # Pharmaceutical
            'therapeutic': 4,    # Pharmaceutical
            'protein': 8,        # Food
            'oil': 8,           # Food
            'coating': 6,        # Materials
            'biodegradable': 6,  # Materials
            'nanoparticle': 9,   # Advanced Tech
            'electrode': 5,      # Energy
            'membrane': 9,       # Advanced Tech
            'antimicrobial': 4,  # Pharmaceutical
            'sustainable': 13,   # Environmental
            'biochar': 13,       # Environmental
            'polymer': 6,        # Materials
            'catalyst': 9,       # Advanced Tech
            'adsorbent': 13      # Environmental
        }
        
        return industry_map.get(keyword, 9)  # Default to Advanced Tech
    
    def map_finding_to_plant_part(self, finding: Dict) -> int:
        """Map research finding to plant part"""
        keyword = finding.get('original_keyword', '').lower()
        full_keyword = finding.get('keyword', '').lower()
        
        # Check if it's actually about cannabinoids
        is_cannabinoid = any(term in full_keyword for term in ['cbd', 'thc', 'cannabinoid', 'cannabidiol'])
        
        # Keyword to plant part mapping
        part_map = {
            'fiber': 1,          # Hemp Bast
            'composite': 1,      # Hemp Bast
            'protein': 2,        # Hemp Seed
            'oil': 3,           # Hemp Seed Oil
            'cellulose': 1,      # Hemp Bast
            'biochar': 8,        # Whole Plant
            'coating': 1,        # Hemp Bast
            'polymer': 1,        # Hemp Bast
            'membrane': 1,       # Hemp Bast
            'nanoparticle': 1,   # Hemp Bast
            'antimicrobial': 1,  # Hemp Bast (unless CBD)
            'adsorbent': 1,      # Hemp Bast
            'catalyst': 1        # Hemp Bast
        }
        
        # Special handling for pharmaceutical/therapeutic
        if keyword in ['pharmaceutical', 'therapeutic', 'extract']:
            if is_cannabinoid:
                return 4  # Cannabinoids
            else:
                # Non-cannabinoid pharma products likely use other parts
                if 'seed' in full_keyword or 'protein' in full_keyword:
                    return 2  # Hemp Seed
                elif 'oil' in full_keyword:
                    return 3  # Hemp Seed Oil
                else:
                    return 1  # Default to Hemp Bast
        
        return part_map.get(keyword, 1)  # Default to Hemp Bast
    
    def calculate_quality_score(self, name: str, description: str, 
                              benefits: List[str], tech_specs: Dict) -> float:
        """Calculate quality score for research-based product"""
        score = 0.0
        
        # Name quality (20%)
        if len(name) >= 10 and len(name) <= 100:
            score += 0.1
        if not any(word in name.lower() for word in ['the the', '  ', 'and and']):
            score += 0.1
        
        # Description quality (30%)
        if len(description) >= 100:
            score += 0.15
        if 'PMID:' in description:  # Has research reference
            score += 0.15
        
        # Benefits quality (20%)
        if len(benefits) >= 3:
            score += 0.1
        if len(benefits) >= 5:
            score += 0.1
        
        # Technical specs quality (20%)
        if len(tech_specs) >= 4:
            score += 0.1
        if 'research_id' in tech_specs and 'publication_year' in tech_specs:
            score += 0.1
        
        # Uniqueness (10%)
        score += 0.1  # Base uniqueness score
        
        return min(score, 1.0)
    
    def is_duplicate(self, product: Dict) -> bool:
        """Check if product already exists"""
        try:
            conn = psycopg2.connect(self.database_url)
            cur = conn.cursor()
            
            # Check by name similarity - lowered threshold for more precision
            cur.execute("""
                SELECT id FROM uses_products
                WHERE similarity(name, %s) > 0.95
                LIMIT 1
            """, (product['name'],))
            
            result = cur.fetchone()
            conn.close()
            
            return result is not None
            
        except Exception as e:
            print(f"Error checking duplicate: {e}")
            return False
    
    def save_product(self, product: Dict) -> bool:
        """Save product to database"""
        if self.is_duplicate(product):
            print(f"  ⚠️  Skipping duplicate: {product['name']}")
            return False
        
        try:
            conn = psycopg2.connect(self.database_url)
            cur = conn.cursor()
            
            # Convert research_id to technical_specs
            tech_specs = product['technical_specifications']
            tech_specs['source_reference'] = product['research_id']
            
            cur.execute("""
                INSERT INTO uses_products (
                    name, description, plant_part_id,
                    industry_sub_category_id, benefits_advantages,
                    technical_specifications, source_agent,
                    created_at
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                RETURNING id
            """, (
                product['name'],
                product['description'],
                product['plant_part_id'],
                product['industry_id'],
                product['benefits'],
                json.dumps(tech_specs),
                product['source_agent'],
                datetime.now()
            ))
            
            product_id = cur.fetchone()[0]
            conn.commit()
            conn.close()
            
            self.discovered_count += 1
            print(f"  ✓ Added: {product['name']} (ID: {product_id})")
            return True
            
        except Exception as e:
            print(f"  ❌ Error saving {product['name']}: {e}")
            return False
    
    def run_discovery_cycle(self):
        """Run research discovery cycle"""
        print(f"\n🔬 {self.name} - Starting Discovery Cycle")
        print("=" * 60)
        
        total_products = 0
        
        for i, topic in enumerate(self.research_topics):
            print(f"\n[{i+1}/{len(self.research_topics)}] Researching: {topic}")
            
            try:
                # Search PubMed
                papers = self.search_pubmed(topic, max_results=5)
                print(f"  Found {len(papers)} research papers")
                
                # Process each paper
                for j, paper in enumerate(papers):
                    print(f"  [{j+1}/{len(papers)}] Processing: {paper.get('title', 'Unknown')[:60]}...")
                    
                    # Generate products
                    products = self.generate_products_from_paper(paper, topic)
                    
                    # Save products
                    for product in products:
                        if self.save_product(product):
                            total_products += 1
                    
                    # Rate limiting
                    time.sleep(0.5)
                
                # Pause between topics
                time.sleep(1)
                
            except Exception as e:
                print(f"  ❌ Error researching topic: {e}")
                continue
        
        print(f"\n✅ Research cycle complete!")
        print(f"Total products added: {total_products}")
        print(f"Agent total: {self.discovered_count}")
        
        return total_products

if __name__ == "__main__":
    agent = AcademicResearchAgent()
    agent.run_discovery_cycle()