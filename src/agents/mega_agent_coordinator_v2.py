#!/usr/bin/env python3
"""
Mega Agent Coordinator V2 - Orchestrates all specialized hemp discovery agents
Manages the expansion from 700 to 50,000+ products
"""

import os
import json
import time
import psycopg2
from datetime import datetime
from typing import Dict, List, Any
import multiprocessing
from concurrent.futures import Thr<PERSON><PERSON><PERSON>Executor, ProcessPoolExecutor
import logging
import sys

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import real agents
from specialized.patent_mining_agent_simple import SimplifiedPatentMiningAgent
from specialized.academic_research_agent import AcademicResearchAgent
from specialized.regional_cultural_agent import RegionalCulturalAgent

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/mega_agent_coordinator_v2.log'),
        logging.StreamHandler()
    ]
)

class MegaAgentCoordinatorV2:
    """Enhanced coordinator with real agent implementations"""
    
    def __init__(self):
        self.name = "Mega Agent Coordinator V2"
        self.version = "2.0.0"
        self.database_url = os.environ.get('DATABASE_URL')
        self.logger = logging.getLogger(self.name)
        
        # Real agent registry - Phase 1 agents ready
        self.agent_registry = {
            "phase_1": {
                "patent_mining": SimplifiedPatentMiningAgent,
                "academic_research": AcademicResearchAgent,
                "regional_cultural": RegionalCulturalAgent
            },
            "phase_2": {
                "industry_specific": None,
                "cross_reference": None
            },
            "phase_3": {
                "emerging_tech": None,
                "niche_discovery": None
            }
        }
        
        # Execution phases
        self.phases = {
            "phase_1": {
                "name": "Foundation Phase",
                "duration_weeks": 4,
                "target_products": 2000,
                "agents": ["patent_mining", "academic_research", "regional_cultural"]
            },
            "phase_2": {
                "name": "Deep Specialization",
                "duration_weeks": 8,
                "target_products": 5000,
                "agents": ["industry_specific", "cross_reference"]
            },
            "phase_3": {
                "name": "Emerging & Niche",
                "duration_weeks": 8,
                "target_products": 8000,
                "agents": ["emerging_tech", "niche_discovery"]
            }
        }
        
        # Performance tracking
        self.performance_stats = {
            "total_discovered": 0,
            "by_phase": {},
            "by_agent": {},
            "quality_scores": [],
            "start_time": datetime.now()
        }
        
    def get_current_stats(self) -> Dict:
        """Get current database statistics"""
        try:
            conn = psycopg2.connect(self.database_url)
            cur = conn.cursor()
            
            # Total products
            cur.execute("SELECT COUNT(*) FROM uses_products")
            total_products = cur.fetchone()[0]
            
            # Products by source agent
            cur.execute("""
                SELECT source_agent, COUNT(*) 
                FROM uses_products 
                WHERE source_agent IS NOT NULL
                GROUP BY source_agent
                ORDER BY COUNT(*) DESC
            """)
            agent_stats = dict(cur.fetchall())
            
            # Recent additions
            cur.execute("""
                SELECT COUNT(*) FROM uses_products
                WHERE created_at > NOW() - INTERVAL '24 hours'
            """)
            recent_24h = cur.fetchone()[0]
            
            # Quality metrics
            cur.execute("""
                SELECT AVG(
                    CASE 
                        WHEN LENGTH(description) > 100 THEN 1.0 
                        ELSE 0.5 
                    END
                ) as quality_score
                FROM uses_products
                WHERE created_at > NOW() - INTERVAL '7 days'
            """)
            recent_quality = cur.fetchone()[0] or 0
            
            conn.close()
            
            return {
                "total_products": total_products,
                "agent_stats": agent_stats,
                "recent_24h": recent_24h,
                "recent_quality": float(recent_quality),
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Error getting stats: {e}")
            return {}
    
    def run_agent(self, agent_type: str, agent_class: Any) -> Dict:
        """Run a single agent and return results"""
        results = {
            "agent_type": agent_type,
            "start_time": datetime.now(),
            "products_discovered": 0,
            "errors": []
        }
        
        try:
            self.logger.info(f"Starting agent: {agent_type}")
            
            # Create agent instance
            agent = agent_class()
            
            # Run discovery
            discovered = agent.run_discovery_cycle()
            
            results["products_discovered"] = discovered
            results["end_time"] = datetime.now()
            results["duration_seconds"] = (results["end_time"] - results["start_time"]).total_seconds()
            
            self.logger.info(f"Agent {agent_type} discovered {discovered} products")
            
            # Update stats
            self.performance_stats["total_discovered"] += discovered
            self.performance_stats["by_agent"][agent_type] = discovered
            
        except Exception as e:
            error_msg = f"Error running {agent_type}: {str(e)}"
            self.logger.error(error_msg)
            results["errors"].append(error_msg)
            
        return results
    
    def run_phase(self, phase_name: str) -> Dict:
        """Run all agents in a phase"""
        phase_config = self.phases.get(phase_name)
        if not phase_config:
            self.logger.error(f"Unknown phase: {phase_name}")
            return {}
            
        self.logger.info(f"Starting {phase_config['name']} - Target: {phase_config['target_products']} products")
        
        phase_results = {
            "phase": phase_name,
            "name": phase_config["name"],
            "start_time": datetime.now(),
            "target": phase_config["target_products"],
            "agents_run": [],
            "total_discovered": 0
        }
        
        # Get starting stats
        start_stats = self.get_current_stats()
        phase_results["start_product_count"] = start_stats.get("total_products", 0)
        
        # Run each agent in the phase
        for agent_name in phase_config["agents"]:
            agent_class = self.agent_registry.get(phase_name, {}).get(agent_name)
            
            if agent_class:
                agent_results = self.run_agent(agent_name, agent_class)
                phase_results["agents_run"].append(agent_results)
                phase_results["total_discovered"] += agent_results["products_discovered"]
            else:
                self.logger.warning(f"Agent {agent_name} not implemented yet")
                phase_results["agents_run"].append({
                    "agent_type": agent_name,
                    "status": "not_implemented"
                })
        
        # Get ending stats
        end_stats = self.get_current_stats()
        phase_results["end_product_count"] = end_stats.get("total_products", 0)
        phase_results["net_increase"] = phase_results["end_product_count"] - phase_results["start_product_count"]
        phase_results["end_time"] = datetime.now()
        phase_results["duration_hours"] = (phase_results["end_time"] - phase_results["start_time"]).total_seconds() / 3600
        
        # Save phase results
        self.save_phase_results(phase_name, phase_results)
        
        return phase_results
    
    def save_phase_results(self, phase_name: str, results: Dict):
        """Save phase results to file"""
        filename = f"logs/expansion_phase_{phase_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        try:
            with open(filename, 'w') as f:
                json.dump(results, f, indent=2, default=str)
            self.logger.info(f"Phase results saved to {filename}")
        except Exception as e:
            self.logger.error(f"Error saving phase results: {e}")
    
    def generate_progress_report(self) -> str:
        """Generate a progress report"""
        stats = self.get_current_stats()
        
        report = f"""
=================================================================
HEMP DATABASE EXPANSION PROGRESS REPORT
=================================================================
Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

OVERALL STATISTICS
------------------
Total Products: {stats.get('total_products', 0)}
Products Added (24h): {stats.get('recent_24h', 0)}
Recent Quality Score: {stats.get('recent_quality', 0):.2f}

AGENT PERFORMANCE
-----------------
"""
        
        for agent, count in stats.get('agent_stats', {}).items():
            report += f"{agent}: {count} products\n"
        
        report += f"""
PHASE PROGRESS
--------------
Phase 1 (Foundation): {'IN PROGRESS' if stats.get('total_products', 0) < 2000 else 'COMPLETED'}
Phase 2 (Deep Spec): {'NOT STARTED'}
Phase 3 (Emerging): {'NOT STARTED'}

TARGET PROGRESS
---------------
Current: {stats.get('total_products', 0)} / 50,000 ({(stats.get('total_products', 0) / 50000 * 100):.1f}%)
Phase 1 Target: 2,000 products
Overall Target: 50,000+ products

=================================================================
"""
        return report
    
    def run_continuous_discovery(self, max_hours: float = 1.0):
        """Run continuous discovery for specified hours"""
        start_time = datetime.now()
        max_seconds = max_hours * 3600
        
        self.logger.info(f"Starting continuous discovery for {max_hours} hours")
        print(self.generate_progress_report())
        
        while (datetime.now() - start_time).total_seconds() < max_seconds:
            # Run available agents
            if SimplifiedPatentMiningAgent:
                self.logger.info("Running patent mining agent...")
                results = self.run_agent("patent_mining", SimplifiedPatentMiningAgent)
                
                # Brief pause between runs
                time.sleep(60)  # 1 minute pause
            
            # Check if we should continue
            elapsed = (datetime.now() - start_time).total_seconds() / 3600
            self.logger.info(f"Elapsed time: {elapsed:.2f} hours")
        
        # Final report
        print(self.generate_progress_report())
        
    def run_interactive_mode(self):
        """Run in interactive mode with menu"""
        while True:
            print("\n" + "="*60)
            print("MEGA AGENT COORDINATOR - Interactive Mode")
            print("="*60)
            print("1. View current statistics")
            print("2. Run Phase 1 agents (Patent Mining)")
            print("3. Run continuous discovery (1 hour)")
            print("4. Generate progress report")
            print("5. Exit")
            print("-"*60)
            
            choice = input("Select option: ")
            
            if choice == "1":
                stats = self.get_current_stats()
                print(f"\nTotal Products: {stats.get('total_products', 0)}")
                print(f"Recent 24h: {stats.get('recent_24h', 0)}")
                print("\nAgent Stats:")
                for agent, count in stats.get('agent_stats', {}).items():
                    print(f"  {agent}: {count}")
                    
            elif choice == "2":
                print("\nRunning Phase 1 agents...")
                results = self.run_phase("phase_1")
                print(f"Phase complete! Discovered {results.get('total_discovered', 0)} products")
                
            elif choice == "3":
                self.run_continuous_discovery(1.0)
                
            elif choice == "4":
                print(self.generate_progress_report())
                
            elif choice == "5":
                print("Exiting...")
                break
                
            else:
                print("Invalid option")

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Mega Agent Coordinator for Hemp Database Expansion")
    parser.add_argument("--phase", choices=["1", "2", "3"], help="Run specific phase")
    parser.add_argument("--continuous", type=float, help="Run continuous discovery for N hours")
    parser.add_argument("--interactive", action="store_true", help="Run in interactive mode")
    parser.add_argument("--report", action="store_true", help="Generate progress report")
    
    args = parser.parse_args()
    
    # Initialize coordinator
    coordinator = MegaAgentCoordinatorV2()
    
    if args.phase:
        results = coordinator.run_phase(f"phase_{args.phase}")
        print(f"Phase {args.phase} complete!")
        print(json.dumps(results, indent=2, default=str))
        
    elif args.continuous:
        coordinator.run_continuous_discovery(args.continuous)
        
    elif args.report:
        print(coordinator.generate_progress_report())
        
    elif args.interactive:
        coordinator.run_interactive_mode()
        
    else:
        # Default: show menu
        coordinator.run_interactive_mode()