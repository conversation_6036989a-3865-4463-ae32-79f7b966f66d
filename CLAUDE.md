# CLAUDE.md

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with code in this repository.

## Current Status (July 8, 2025 - Night Update)

### 🚀 Expansion Initiative FULLY AUTOMATED!
- **Database Growth**: 926 products (up from 761, +21.7% today!)
- **All Phase 1 Agents Live**: Patent, Academic, and Cultural agents operational
- **Automation Active**: Cron job running hourly 24/7
- **Quality Maintained**: 100% quality score on new products
- **Phase 1 Progress**: 46.3% complete (926/2,000 products)
- **Growth Rate**: 212 products/day with multiple agents

### 📊 Database Status
- **Products**: 926 total (212 added in last 24h)
- **Quality Score**: 1.0/1.0 for recent additions
- **Patent Database**: Expanded to 90+ patents (from 10)
- **Agent Coverage**: 3 specialized agents running
- **Automation**: Hourly cron job installed and active
- **Projected Phase 1 Completion**: ~5 days

### 🤖 Active Systems
1. **Patent Mining Agent V2**: 90+ patent database, 124 products added
2. **Academic Research Agent**: PubMed integration, 39 products added
3. **Regional/Cultural Agent**: 15 cultures, 45+ traditional uses documented
4. **Mega Agent Coordinator V2**: Orchestrating all agents
5. **Automated Scheduling**: Cron job running every hour
6. **Quality Control**: 100% validation on all new products

### 🎯 Today's Major Achievements (July 8, 2025)
- **Expanded Patent Database**: From 10 to 90+ patents
- **Implemented Academic Agent**: Mining PubMed research papers
- **Deployed Cultural Agent**: Preserving traditional hemp knowledge
- **Automated Everything**: Cron job for 24/7 expansion
- **Added 165 Products**: Via automated agent runs
- **System Running Independently**: No manual intervention needed

## Quick Start Commands

```bash
# Development
cd HempResourceHub
npm install

# Start servers (need 2 terminals)
# Terminal 1: Backend
npm run dev                    # Starts Express server on :3001

# Terminal 2: Frontend  
npx vite                       # Starts Vite dev server on :5173

# Access the app at http://localhost:5173

# Database
npm run db:push               # Push schema changes
psql $DATABASE_URL -f migrations/add_deduplication_fields.sql  # Add dedup fields

# Multi-Provider AI & Automation
source venv_dedup/bin/activate
./RUN_AUTOMATION.sh                        # Start hourly automation (easiest)
python monitor_automation.py               # Check automation status
python simple_agent_dashboard.py           # Agent performance dashboard
tail -f logs/automation_hourly_*.log       # View live automation logs

# AI-Powered Agents
python launch_enhanced_ai_agent.py         # Enhanced multi-provider agent
python demo_ai_providers.py                # Test AI providers
python check_product_images.py             # Check image coverage

# Cloud Deployment
python export_database.py                  # Export for cloud migration
cat CLOUD_DEPLOYMENT_GUIDE.md              # Cloud setup instructions

# Quality & Cleanup (UPDATED!)
python comprehensive_data_quality_analysis.py  # Full quality report
python comprehensive_database_cleanup.py       # One-time full cleanup
python enhanced_quality_control_pipeline.py    # Test quality validation
python safe_automation_runner.py               # Run safe automation
python verify_cleanup.py                       # Verify database quality

# Image Management (NEW!)
python generate_product_images.py             # Generate AI images with Replicate
python expanded_product_image_search.py       # Find real product images
python smart_image_switcher.py                # Switch between AI/real images
python check_image_status.py                  # Check image coverage

# Unified CLI
./hemp agent research "query" --features company image
./hemp images generate --provider stable-diffusion
./hemp monitor --live
./hemp db export --format json

# Enhanced Scrapers
python enhanced_research_scraper.py        # PubMed integration
python enhanced_company_scraper.py         # Logo extraction

# NEW: Database Expansion (761 → 50,000 Products) - ACTIVE!
python src/agents/specialized/patent_mining_agent_simple.py   # Patent discovery (WORKING!)
python src/agents/mega_agent_coordinator_v2.py --interactive  # Interactive coordinator
python src/agents/mega_agent_coordinator_v2.py --phase 1      # Run Phase 1 agents
python src/agents/mega_agent_coordinator_v2.py --report       # Progress report
python analyze_database_gaps.py                               # Find expansion opportunities
```

## Project Architecture

### Tech Stack
- **Frontend**: React + TypeScript + Vite + Tailwind CSS
- **Backend**: Express.js + Drizzle ORM
- **Database**: PostgreSQL via Supabase
- **UI**: shadcn/ui components + custom animations
- **State**: React Query (TanStack Query)

### Database Schema
```
hemp_plant_archetypes → plant_parts → uses_products
                                    ↘ industry_sub_categories
                                    ↘ hemp_companies
                                    ↘ research_entries
```

### Key Tables (Actual Names)
- `hemp_plant_archetypes` (NOT plant_types)
- `uses_products` (NOT hemp_products)
- `industry_sub_categories` (NOT sub_industries)
- `research_entries` (NOT research_papers)
- `plant_parts` with `plant_type_id` FK

## Recent Features & Fixes

### UI/UX Enhancements (Augment Code)
- **Navigation**: Simplified to single `/products` route
- **Admin Panel**: 9→5 tabs with dropdown selectors
- **Visual**: Modern cards, gradients, hemp growing loader
- **Performance**: 53% navigation complexity reduction
- **Animations**: 4 new components (hemp-growing-loader, etc.)

### Data & Backend (Claude)
- **Product Discovery**: Manual + Python scripts
- **Company System**: Extraction, relationships, deduplication
- **Image Generation**: Integrated with research agent
- **Attribution**: Legal compliance for scraped content

### Fixed Issues
1. ✅ Database table name mismatches
2. ✅ Image display with proper fallbacks
3. ✅ Duplicate image generation loop
4. ✅ Research frontend column mapping
5. ✅ SSL/authentication for development
6. ✅ Content Security Policy for fonts
7. ✅ All npm vulnerabilities (prismjs, esbuild)
8. ✅ GitHub Actions updated to latest versions
9. ✅ SSL certificate issues for Drizzle database connections
10. ✅ Claude API credit errors (service temporarily disabled)
11. ✅ TypeScript component errors (25+ fixed)
12. ✅ Supabase client API usage (.table → .from)
13. ✅ Property name mappings (industryId, benefits_advantages, etc.)
14. ✅ Missing module implementations
15. ✅ Deduplication system (found 66 duplicates)
16. ✅ Plant-part agent architecture designed
17. ✅ Database migration for versioning ready

## Environment Variables

```bash
# Required
VITE_SUPABASE_URL=https://ktoqznqmlnxrtvubewyz.supabase.co
VITE_SUPABASE_ANON_KEY=[from Supabase dashboard]
DATABASE_URL=postgresql://postgres:[password]@db.ktoqznqmlnxrtvubewyz.supabase.co:5432/postgres

# Optional
SUPABASE_SERVICE_ROLE_KEY=[for admin operations]
OPENAI_API_KEY=[for AI features]
```

## Development Workflow

1. **Frontend Changes**: Edit components in `/client/src/`, hot-reload via Vite
2. **Database Changes**: Update `/shared/schema.ts`, run `npm run db:push`
3. **API Changes**: Update `/server/routes.ts` and `/server/storage-db.ts`
4. **Python Scripts**: Use for data population and scraping
5. **Testing**: `npm run test` for API tests

## Key Components & Files

### Frontend
- `/client/src/components/ui/` - Reusable UI components
- `/client/src/components/animations/` - Hemp-themed animations
- `/client/src/pages/` - Route page components
- `/client/src/hooks/` - React Query data hooks

### Backend
- `/server/index.ts` - Express server setup
- `/server/routes.ts` - API endpoints
- `/server/storage-db.ts` - Database queries
- `/shared/schema.ts` - Shared type definitions

### Python Scripts
- `enhanced_research_scraper.py` - PubMed/article scraping
- `enhanced_company_scraper.py` - Logo extraction
- `run_simple_scrapers.py` - Pipeline runner
- `quick_add_product.py` - Manual product entry

### UI Components Created
- `bento-grid.tsx` - Classic bento grid homepage layout (NEW!)
- `bento-grid-v2.tsx` - Enhanced bento with animations (NEW!)
- `smart-product-image.tsx` - Intelligent image with fallbacks
- `enhanced-product-card.tsx` - Responsive cards with benefits
- `attributed-image.tsx` - Image with source attribution
- `hemp-growing-loader.tsx` - Plant growth animation
- `smart-search.tsx` - AI-powered search
- `interactive-product-card.tsx` - Enhanced cards
- `data-visualization-dashboard.tsx` - Analytics
- `alphabet-filter.tsx` - A-Z filtering component

## Common Tasks

### Adding Products
```python
# Manual with companies
python quick_add_product.py

# Bulk import from CSV
python bulk_import_products.py

# Via research agent
python run_agent_with_images.py
```

### Running Scrapers
```python
# Company logos
python simple_logo_scraper.py

# Research images
python hemp_industry_daily_scraper.py

# Full pipeline
python run_simple_scrapers.py
```

### Troubleshooting

#### Puppeteer in WSL
```bash
sudo apt-get update
sudo apt-get install -y chromium-browser
sudo apt-get install -y libnss3 libnspr4 libatk1.0-0 libatk-bridge2.0-0
```

#### Database Connection
- Check URL encoding for special characters in password
- Use `sslmode=disable` for local development
- Ensure all environment variables are set

#### Image Issues
- Verify `/client/public/images/` directory exists
- Check for proper `?url` suffix in Vite imports
- Use fallback images for missing content

#### Express Server API Flooding
If you see rapid POST requests to `/api/ai/conversations`:
- This happens when AI services are unavailable but frontend keeps retrying
- Fix: AI routes now return 503 Service Unavailable with proper error messages
- Frontend hooks stop retrying on 503 errors
- AI components temporarily disabled in admin and product pages
- Use Python agents instead: `python launch_enhanced_ai_agent.py`

## Important Notes

- Always use `plant_type_id` not `archetype_id` (DB uses the former)
- Products table is `uses_products` not `hemp_products`
- Research table is `research_entries` not `research_papers`
- Use SERVICE_ROLE_KEY for admin operations (bypasses RLS)
- Keep git commits under 50MB (no node_modules)

### AI Service Status (July 1, 2025)
- **Claude API**: Temporarily disabled due to insufficient credits
- **To Re-enable**:
  1. Add credits to Anthropic account
  2. Uncomment line 12 in `/server/routes.ts`
  3. Uncomment line 400 in `/server/routes.ts`
- **SSL Fix for Drizzle**: Use `NODE_TLS_REJECT_UNAUTHORIZED=0 npm run db:push`

### Known Issues (July 3, 2025)
- **TypeScript Errors**: ~16 remaining errors (mostly server-side) - down from 229
- **Missing npm lint script**: Add ESLint configuration
- **SSL Certificate Warning**: Requires NODE_TLS_REJECT_UNAUTHORIZED=0 for DB operations
- **Fixed Issues**: 
  - ✅ All component TypeScript errors resolved
  - ✅ Express API flooding issue (was 100+ requests/second)
  - ✅ AI service unavailable errors now handled gracefully

## 🎯 Recommended Next Steps

### ACTIVE: Database Expansion Initiative (761 → 50,000 Products)

Expansion is LIVE! Patent agent has added 47 products today. Here's what's next:

1. **Expand Patent Database** - Current agent limited to 10 patents
   ```python
   # Add more patents to: src/agents/specialized/patent_mining_agent_simple.py
   # Then run: python src/agents/mega_agent_coordinator_v2.py --phase 1
   ```

2. **Implement Missing Phase 1 Agents**
   - ❌ Academic Research Agent (PubMed, papers)
   - ❌ Regional/Cultural Agent (traditional uses)
   - ✅ Patent Mining Agent (47 products added!)
   - See `EXPANSION_PROGRESS_ANALYSIS.md` for details

3. **Schedule Automated Runs**
   ```bash
   # Set up cron job for hourly runs:
   0 * * * * cd /path/to/project && source venv_dedup/bin/activate && export DATABASE_URL="..." && python src/agents/mega_agent_coordinator_v2.py --continuous 0.5
   
   # Monitor progress:
   python src/agents/mega_agent_coordinator_v2.py --report
   tail -f logs/mega_agent_coordinator_v2.log
   ```

### Previous Priorities (Still Valid)
1. **Complete Missing Images** - If any remain
2. **Fix Company Associations** - 232 products need companies
3. **Expand Research Database** - Integrate with academic agents

### Short-term Goals (Month 1)
1. **Launch API Service** - Monetization opportunity
2. **Implement B2B Features** - Supplier directory, RFQ system
3. **Mobile App Development** - iOS/Android hemp database app

### Revenue Opportunities
- API Access: $99-999/month
- Premium Data: Enhanced details
- Lead Generation: Connect buyers/sellers
- Projected: $10K MRR within 6 months

See `HEMP_DATABASE_EVALUATION_2025.md` for detailed roadmap.

## Support & Feedback

- **Help**: Use `/help` command
- **Issues**: Report at https://github.com/anthropics/claude-code/issues
- **Docs**: https://docs.anthropic.com/en/docs/claude-code